from flask import Blueprint, jsonify
from flask_jwt_extended import jwt_required, get_jwt_identity
import random
from datetime import datetime, timedelta

tests_bp = Blueprint('tests', __name__)

# In-memory storage for tests (replace with database in production)
tests = []

def generate_sample_tests():
    test_types = ['API', 'Security', 'Performance', 'Integration']
    statuses = ['passed', 'failed', 'running']
    projects = ['Project A', 'Project B', 'Project C', 'Project D']
    
    for i in range(20):
        test = {
            'id': i + 1,
            'name': f'Test {i + 1}',
            'project_name': random.choice(projects),
            'type': random.choice(test_types),
            'status': random.choice(statuses),
            'duration': random.randint(1, 300),
            'started_at': (datetime.now() - timedelta(days=random.randint(0, 7))).isoformat(),
            'details': {
                'total_steps': random.randint(5, 20),
                'passed_steps': random.randint(0, 20),
                'failed_steps': random.randint(0, 5),
                'error_message': 'No errors' if random.random() > 0.3 else 'Test failed due to timeout'
            }
        }
        tests.append(test)

# Generate sample tests on module import
generate_sample_tests()

@tests_bp.route('/api/tests', methods=['GET'])
@jwt_required()
def get_tests():
    try:
        current_user = get_jwt_identity()
        return jsonify(tests), 200
    except Exception as e:
        return jsonify({'message': str(e)}), 500

@tests_bp.route('/api/tests/<int:test_id>', methods=['GET'])
@jwt_required()
def get_test_details(test_id):
    try:
        current_user = get_jwt_identity()
        test = next((t for t in tests if t['id'] == test_id), None)
        if not test:
            return jsonify({'message': 'Test not found'}), 404
        return jsonify(test), 200
    except Exception as e:
        return jsonify({'message': str(e)}), 500 