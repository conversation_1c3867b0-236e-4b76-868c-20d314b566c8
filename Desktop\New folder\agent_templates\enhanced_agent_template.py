"""
Enhanced template for dynamically generated agents.
This file serves as a template that will be customized when creating new agents.
"""

import os
import json
import uuid
from datetime import datetime

class DynamicAgent:
    """
    Base class for dynamically generated agents.
    This class provides the core functionality that all dynamic agents will have.
    """
    
    def __init__(self, agent_type, capabilities, requirements=None):
        """
        Initialize the dynamic agent.
        
        Args:
            agent_type (str): Type of the agent (e.g., 'frontend', 'backend', 'specialized')
            capabilities (list): List of capabilities this agent has
            requirements (dict, optional): Requirements for the agent
        """
        # Generate a unique ID for this agent
        self.agent_id = f"{agent_type}_agent_{uuid.uuid4().hex[:8]}"
        
        # Store agent information
        self.agent_type = agent_type
        self.capabilities = capabilities
        self.requirements = requirements or {}
        
        # Initialize agent state
        self.status = 'initialized'
        self.creation_time = datetime.now().isoformat()
        self.last_activity = self.creation_time
        
        # Initialize performance metrics
        self.performance_metrics = {
            'tasks_completed': 0,
            'success_rate': 0.0,
            'average_response_time': 0.0
        }
        
        # Initialize task history
        self.task_history = []
        
        # Create logs directory if it doesn't exist
        os.makedirs('logs/agents', exist_ok=True)
        
        # Log agent creation
        self._log_activity('agent_created', {
            'agent_id': self.agent_id,
            'agent_type': self.agent_type,
            'capabilities': self.capabilities
        })
        
        print(f"Dynamic {agent_type.capitalize()} Agent initialized with ID: {self.agent_id}")
    
    def execute_task(self, task_name, task_params=None):
        """
        Execute a task based on the agent's capabilities.
        
        Args:
            task_name (str): Name of the task to execute
            task_params (dict, optional): Parameters for the task
            
        Returns:
            dict: Result of the task execution
        """
        # Update agent status
        self.status = 'executing_task'
        
        # Log task start
        self._log_activity('task_started', {
            'task_name': task_name,
            'task_params': task_params
        })
        
        # Check if the agent has the capability to execute this task
        if task_name not in self.capabilities:
            result = {
                'status': 'error',
                'message': f"Agent does not have capability: {task_name}",
                'task_name': task_name
            }
            
            # Log task failure
            self._log_activity('task_failed', result)
            
            # Update agent status
            self.status = 'ready'
            
            return result
        
        try:
            # Execute the task if the agent has a method for it
            if hasattr(self, task_name) and callable(getattr(self, task_name)):
                task_method = getattr(self, task_name)
                result = task_method(task_params or {})
            else:
                # Default implementation for unknown tasks
                result = self._default_task_execution(task_name, task_params or {})
            
            # Update performance metrics
            self.performance_metrics['tasks_completed'] += 1
            
            # Calculate success rate
            success = result.get('status') == 'success'
            total_tasks = len(self.task_history) + 1
            self.performance_metrics['success_rate'] = (
                (self.performance_metrics['success_rate'] * (total_tasks - 1) + (1 if success else 0)) / total_tasks
            )
            
            # Add to task history
            self.task_history.append({
                'task_name': task_name,
                'task_params': task_params,
                'result_status': result.get('status'),
                'timestamp': datetime.now().isoformat()
            })
            
            # Log task completion
            self._log_activity('task_completed', {
                'task_name': task_name,
                'result': result
            })
            
            # Update agent status
            self.status = 'ready'
            
            return result
            
        except Exception as e:
            # Handle any exceptions during task execution
            error_message = str(e)
            
            result = {
                'status': 'error',
                'message': f"Error executing task: {error_message}",
                'task_name': task_name
            }
            
            # Log task error
            self._log_activity('task_error', {
                'task_name': task_name,
                'error': error_message
            })
            
            # Update agent status
            self.status = 'error'
            
            return result
    
    def _default_task_execution(self, task_name, task_params):
        """
        Default implementation for task execution.
        This method will be called if the agent doesn't have a specific method for the task.
        
        Args:
            task_name (str): Name of the task to execute
            task_params (dict): Parameters for the task
            
        Returns:
            dict: Result of the task execution
        """
        return {
            'status': 'success',
            'message': f"Executed task {task_name} with default implementation",
            'task_name': task_name,
            'task_params': task_params
        }
    
    def get_status(self):
        """
        Get the current status of the agent.
        
        Returns:
            dict: Status information
        """
        return {
            'agent_id': self.agent_id,
            'agent_type': self.agent_type,
            'status': self.status,
            'capabilities': self.capabilities,
            'performance_metrics': self.performance_metrics,
            'last_activity': self.last_activity
        }
    
    def update_capabilities(self, new_capabilities):
        """
        Update the agent's capabilities.
        
        Args:
            new_capabilities (list): New capabilities to add
            
        Returns:
            dict: Updated capabilities
        """
        # Add new capabilities
        for capability in new_capabilities:
            if capability not in self.capabilities:
                self.capabilities.append(capability)
        
        # Log capability update
        self._log_activity('capabilities_updated', {
            'capabilities': self.capabilities
        })
        
        return {
            'status': 'success',
            'message': 'Capabilities updated successfully',
            'capabilities': self.capabilities
        }
    
    def _log_activity(self, activity_type, data):
        """
        Log agent activities for monitoring and debugging.
        
        Args:
            activity_type (str): Type of activity
            data (dict): Activity data
        """
        log_entry = {
            "timestamp": datetime.now().isoformat(),
            "agent_id": self.agent_id,
            "agent_type": self.agent_type,
            "activity_type": activity_type,
            "data": data
        }
        
        # Update last activity timestamp
        self.last_activity = log_entry["timestamp"]
        
        # Write to log file
        log_file = f"logs/agents/{self.agent_id}_{datetime.now().strftime('%Y%m%d')}.log"
        with open(log_file, 'a') as f:
            f.write(json.dumps(log_entry) + "\n")

# This is where specialized agent classes will be defined
class DynamicSpecializedAgent(DynamicAgent):
    """
    Specialized agent class that extends the base DynamicAgent.
    This class will be customized based on the agent type and capabilities.
    """
    
    def __init__(self, agent_type, capabilities, requirements=None):
        """Initialize the specialized agent."""
        super().__init__(agent_type, capabilities, requirements)
        
        # Additional specialized initialization
        self.specialization = agent_type
        
        # Log specialization
        self._log_activity('agent_specialized', {
            'specialization': self.specialization
        })
