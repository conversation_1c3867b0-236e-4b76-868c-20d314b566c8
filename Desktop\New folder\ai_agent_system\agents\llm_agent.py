"""
LLM Agent Module

This module provides an agent that can use LLMs for various tasks.
"""

import logging
from typing import Dict, List, Any, Optional

from ai_agent_system.core.base_agent import BaseAgent
from ai_agent_system.llm.llm_manager import LL<PERSON>anager

# Configure logging
logger = logging.getLogger(__name__)

class LLMAgent(BaseAgent):
    """
    Agent that can use LLMs for various tasks.
    """

    def __init__(self, agent_id: Optional[str] = None, capabilities: Optional[List[str]] = None, **kwargs):
        """
        Initialize the LLM agent.

        Args:
            agent_id (str, optional): Unique identifier for the agent
            capabilities (list, optional): List of capabilities for the agent
            **kwargs: Additional arguments to pass to the parent constructor
        """
        # Set default capabilities if not provided
        if capabilities is None:
            capabilities = [
                'text_generation',
                'code_generation',
                'agent_code_generation',
                'summarization',
                'translation',
                'question_answering'
            ]

        # Initialize the parent class
        super().__init__(agent_id=agent_id, capabilities=capabilities, **kwargs)

        # Initialize LLM manager
        self.llm_manager = LLMManager()

        # Get default provider
        self.default_provider = self.llm_manager.default_provider_name

        logger.info(f"LLM Agent initialized with default provider: {self.default_provider}")

    def text_generation(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """
        Generate text using an LLM.

        Args:
            params (dict): Parameters for text generation
                - prompt (str): The prompt to send to the LLM
                - provider (str, optional): The LLM provider to use
                - options (dict, optional): Additional options for the LLM

        Returns:
            dict: Text generation result
        """
        # Extract parameters
        prompt = params.get('prompt')
        provider = params.get('provider', self.default_provider)
        options = params.get('options', {})

        if not prompt:
            return {
                'status': 'error',
                'message': "Prompt is required for text generation"
            }

        try:
            # Generate text using the LLM manager
            result = self.llm_manager.generate_text(prompt, provider, options)

            return result

        except Exception as e:
            logger.error(f"Error generating text: {e}")
            return {
                'status': 'error',
                'message': f"Error generating text: {str(e)}"
            }

    def code_generation(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """
        Generate code using an LLM.

        Args:
            params (dict): Parameters for code generation
                - prompt (str): The prompt to send to the LLM
                - language (str, optional): The programming language to generate
                - provider (str, optional): The LLM provider to use
                - options (dict, optional): Additional options for the LLM

        Returns:
            dict: Code generation result
        """
        # Extract parameters
        prompt = params.get('prompt')
        language = params.get('language')
        provider = params.get('provider', self.default_provider)
        options = params.get('options', {})

        if not prompt:
            return {
                'status': 'error',
                'message': "Prompt is required for code generation"
            }

        try:
            # Generate code using the LLM manager
            result = self.llm_manager.generate_code(prompt, language, provider, options)

            return result

        except Exception as e:
            logger.error(f"Error generating code: {e}")
            return {
                'status': 'error',
                'message': f"Error generating code: {str(e)}"
            }

    def agent_code_generation(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """
        Generate code for an agent using an LLM.

        Args:
            params (dict): Parameters for agent code generation
                - agent_type (str): The type of agent to generate
                - capabilities (list): List of capabilities the agent should have
                - requirements (dict, optional): Specific requirements for the agent
                - provider (str, optional): The LLM provider to use
                - options (dict, optional): Additional options for the LLM

        Returns:
            dict: Agent code generation result
        """
        # Extract parameters
        agent_type = params.get('agent_type')
        capabilities = params.get('capabilities', [])
        requirements = params.get('requirements')
        provider = params.get('provider', self.default_provider)
        options = params.get('options', {})

        if not agent_type:
            return {
                'status': 'error',
                'message': "Agent type is required for agent code generation"
            }

        if not capabilities:
            return {
                'status': 'error',
                'message': "At least one capability is required for agent code generation"
            }

        try:
            # Generate agent code using the LLM manager
            result = self.llm_manager.generate_agent_code(agent_type, capabilities, requirements, provider, options)

            return result

        except Exception as e:
            logger.error(f"Error generating agent code: {e}")
            return {
                'status': 'error',
                'message': f"Error generating agent code: {str(e)}"
            }

    def summarization(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """
        Summarize text using an LLM.

        Args:
            params (dict): Parameters for summarization
                - text (str): The text to summarize
                - max_length (int, optional): Maximum length of the summary
                - provider (str, optional): The LLM provider to use
                - options (dict, optional): Additional options for the LLM

        Returns:
            dict: Summarization result
        """
        # Extract parameters
        text = params.get('text')
        max_length = params.get('max_length', 200)
        provider = params.get('provider', self.default_provider)
        options = params.get('options', {})

        if not text:
            return {
                'status': 'error',
                'message': "Text is required for summarization"
            }

        try:
            # Create a summarization prompt
            prompt = f"Summarize the following text in {max_length} words or less:\n\n{text}"

            # Generate summary using the LLM manager
            result = self.llm_manager.generate_text(prompt, provider, options)

            if result['status'] == 'success':
                return {
                    'status': 'success',
                    'summary': result['text'],
                    'model': result['model'],
                    'usage': result.get('usage')
                }

            return result

        except Exception as e:
            logger.error(f"Error summarizing text: {e}")
            return {
                'status': 'error',
                'message': f"Error summarizing text: {str(e)}"
            }

    def translation(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """
        Translate text using an LLM.

        Args:
            params (dict): Parameters for translation
                - text (str): The text to translate
                - source_language (str, optional): The source language
                - target_language (str): The target language
                - provider (str, optional): The LLM provider to use
                - options (dict, optional): Additional options for the LLM

        Returns:
            dict: Translation result
        """
        # Extract parameters
        text = params.get('text')
        source_language = params.get('source_language')
        target_language = params.get('target_language')
        provider = params.get('provider', self.default_provider)
        options = params.get('options', {})

        if not text:
            return {
                'status': 'error',
                'message': "Text is required for translation"
            }

        if not target_language:
            return {
                'status': 'error',
                'message': "Target language is required for translation"
            }

        try:
            # Create a translation prompt
            if source_language:
                prompt = f"Translate the following {source_language} text to {target_language}:\n\n{text}"
            else:
                prompt = f"Translate the following text to {target_language}:\n\n{text}"

            # Generate translation using the LLM manager
            result = self.llm_manager.generate_text(prompt, provider, options)

            if result['status'] == 'success':
                return {
                    'status': 'success',
                    'translation': result['text'],
                    'source_language': source_language,
                    'target_language': target_language,
                    'model': result['model'],
                    'usage': result.get('usage')
                }

            return result

        except Exception as e:
            logger.error(f"Error translating text: {e}")
            return {
                'status': 'error',
                'message': f"Error translating text: {str(e)}"
            }

    def question_answering(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """
        Answer a question using an LLM.

        Args:
            params (dict): Parameters for question answering
                - question (str): The question to answer
                - context (str, optional): Additional context for the question
                - provider (str, optional): The LLM provider to use
                - options (dict, optional): Additional options for the LLM

        Returns:
            dict: Question answering result
        """
        # Extract parameters
        question = params.get('question')
        context = params.get('context')
        provider = params.get('provider', self.default_provider)
        options = params.get('options', {})

        if not question:
            return {
                'status': 'error',
                'message': "Question is required for question answering"
            }

        try:
            # Create a question answering prompt
            if context:
                prompt = f"Answer the following question based on the provided context:\n\nContext: {context}\n\nQuestion: {question}"
            else:
                prompt = f"Answer the following question:\n\n{question}"

            # Generate answer using the LLM manager
            result = self.llm_manager.generate_text(prompt, provider, options)

            if result['status'] == 'success':
                return {
                    'status': 'success',
                    'answer': result['text'],
                    'model': result['model'],
                    'usage': result.get('usage')
                }

            return result

        except Exception as e:
            logger.error(f"Error answering question: {e}")
            return {
                'status': 'error',
                'message': f"Error answering question: {str(e)}"
            }
