from datetime import datetime
from database import db

class Plan(db.Model):
    """Subscription plan model."""
    __tablename__ = 'plans'

    id = db.Column(db.String(50), primary_key=True)  # e.g., 'basic', 'professional', 'enterprise'
    name = db.Column(db.String(100), nullable=False)
    description = db.Column(db.Text)
    price = db.Column(db.Float, nullable=False)
    stripe_price_id = db.Column(db.String(100), nullable=False)
    features = db.Column(db.JSON, nullable=False)
    is_active = db.Column(db.<PERSON>an, default=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    subscriptions = db.relationship('Subscription', backref='plan_details', lazy=True)

class Subscription(db.Model):
    """User subscription model."""
    __tablename__ = 'subscriptions'

    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    plan_id = db.Column(db.String(50), db.ForeignKey('plans.id'), nullable=False)
    stripe_subscription_id = db.Column(db.String(100), unique=True)
    stripe_customer_id = db.Column(db.String(100))
    status = db.Column(db.String(50), nullable=False)  # active, canceled, past_due, etc.
    current_period_start = db.Column(db.DateTime, nullable=False)
    current_period_end = db.Column(db.DateTime, nullable=False)
    cancel_at_period_end = db.Column(db.Boolean, default=False)
    canceled_at = db.Column(db.DateTime)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # Usage tracking
    projects_count = db.Column(db.Integer, default=0)
    api_calls_count = db.Column(db.Integer, default=0)
    agents_count = db.Column(db.Integer, default=0)

    def is_active(self):
        """Check if subscription is active."""
        return (
            self.status == 'active' and
            datetime.utcnow() <= self.current_period_end
        )

    def can_create_project(self):
        """Check if user can create more projects."""
        if not self.is_active():
            return False
        
        limit = self.plan_details.features.get('projects_limit')
        if limit == -1:  # Unlimited
            return True
        
        return self.projects_count < limit

    def can_create_agent(self):
        """Check if user can create more agents."""
        if not self.is_active():
            return False
        
        limit = self.plan_details.features.get('agents_per_project')
        if limit == -1:  # Unlimited
            return True
        
        return self.agents_count < limit

    def can_make_api_call(self):
        """Check if user can make more API calls."""
        if not self.is_active():
            return False
        
        limit = self.plan_details.features.get('api_calls_per_month')
        if limit == -1:  # Unlimited
            return True
        
        return self.api_calls_count < limit

    def increment_usage(self, metric, amount=1):
        """Increment usage for a specific metric."""
        if metric == 'projects':
            self.projects_count += amount
        elif metric == 'api_calls':
            self.api_calls_count += amount
        elif metric == 'agents':
            self.agents_count += amount
        
        db.session.commit()

class Invoice(db.Model):
    """Invoice model for subscription payments."""
    __tablename__ = 'invoices'

    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    subscription_id = db.Column(db.Integer, db.ForeignKey('subscriptions.id'), nullable=False)
    stripe_invoice_id = db.Column(db.String(100), unique=True)
    amount = db.Column(db.Float, nullable=False)
    currency = db.Column(db.String(3), nullable=False)
    status = db.Column(db.String(50), nullable=False)
    invoice_pdf = db.Column(db.String(255))
    period_start = db.Column(db.DateTime, nullable=False)
    period_end = db.Column(db.DateTime, nullable=False)
    paid_at = db.Column(db.DateTime)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

class UsageLog(db.Model):
    """Log for tracking detailed usage."""
    __tablename__ = 'usage_logs'

    id = db.Column(db.Integer, primary_key=True)
    subscription_id = db.Column(db.Integer, db.ForeignKey('subscriptions.id'), nullable=False)
    metric = db.Column(db.String(50), nullable=False)  # projects, api_calls, agents
    amount = db.Column(db.Integer, nullable=False)
    description = db.Column(db.String(255))
    timestamp = db.Column(db.DateTime, default=datetime.utcnow)

    @classmethod
    def log_usage(cls, subscription_id, metric, amount=1, description=None):
        """Create a new usage log entry."""
        log = cls(
            subscription_id=subscription_id,
            metric=metric,
            amount=amount,
            description=description
        )
        db.session.add(log)
        db.session.commit()

        # Update subscription usage
        subscription = Subscription.query.get(subscription_id)
        subscription.increment_usage(metric, amount)
        return log 