from flask import Blueprint, jsonify
from flask_jwt_extended import jwt_required, get_jwt_identity
import random
from datetime import datetime, timedelta

analytics_bp = Blueprint('analytics', __name__)

@analytics_bp.route('/api/analytics', methods=['GET'])
@jwt_required()
def get_analytics():
    try:
        current_user = get_jwt_identity()
        
        # Generate timestamps for the last 30 days
        end_date = datetime.now()
        start_date = end_date - timedelta(days=30)
        dates = [(start_date + timedelta(days=x)).strftime('%Y-%m-%d') for x in range(31)]
        
        # Generate sample analytics data
        analytics_data = {
            'api_calls': {
                'labels': dates,
                'data': [random.randint(100, 1000) for _ in range(31)]
            },
            'storage_usage': {
                'labels': dates,
                'data': [random.randint(50, 500) for _ in range(31)]
            },
            'active_users': {
                'labels': dates,
                'data': [random.randint(10, 100) for _ in range(31)]
            },
            'error_rates': {
                'labels': dates,
                'data': [round(random.uniform(0.1, 5.0), 2) for _ in range(31)]
            },
            'summary': {
                'total_api_calls': sum([random.randint(100, 1000) for _ in range(31)]),
                'avg_response_time': round(random.uniform(100, 500), 2),
                'success_rate': round(random.uniform(95, 99.9), 2),
                'error_rate': round(random.uniform(0.1, 5.0), 2)
            }
        }
        
        return jsonify(analytics_data), 200
        
    except Exception as e:
        return jsonify({'message': str(e)}), 500 