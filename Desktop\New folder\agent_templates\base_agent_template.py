"""
Base Agent Template

This template can be used to create new agents with the AI Agent System.
"""

from ai_agent_system.core.base_agent import BaseAgent

class {agent_type}Agent(BaseAgent):
    """
    {agent_type_capitalized} agent for the AI agent system.
    """

    def __init__(self, agent_id=None, capabilities=None, **kwargs):
        """
        Initialize the {agent_type} agent.

        Args:
            agent_id (str, optional): Unique identifier for the agent
            capabilities (list, optional): List of capabilities for the agent
            **kwargs: Additional arguments to pass to the parent constructor
        """
        # Set default capabilities if not provided
        if capabilities is None:
            capabilities = [{capabilities_list}]

        # Initialize the parent class
        super().__init__(agent_id=agent_id, agent_type='{agent_type}', capabilities=capabilities, **kwargs)

    def execute_task(self, task_name, task_params=None):
        """
        Execute a task based on the agent's capabilities.

        Args:
            task_name (str): Name of the task to execute
            task_params (dict, optional): Parameters for the task

        Returns:
            dict: Result of the task execution
        """
        # Update agent status
        self.status = 'executing_task'

        # Log task start
        self._log_activity('task_started', {
            'task_name': task_name,
            'task_params': task_params
        })

        # Check if the agent has the capability to execute this task
        if task_name not in self.capabilities:
            result = {
                'status': 'error',
                'message': f"Agent does not have capability: {task_name}",
                'task_name': task_name
            }

            # Log task failure
            self._log_activity('task_failed', result)

            # Update agent status
            self.status = 'ready'

            return result

        try:
            # Execute the task if the agent has a method for it
            if hasattr(self, task_name) and callable(getattr(self, task_name)):
                task_method = getattr(self, task_name)
                result = task_method(task_params or {})
            else:
                # Default implementation for unknown tasks
                result = self._default_task_execution(task_name, task_params or {})

            # Update performance metrics
            self.performance_metrics['tasks_completed'] += 1

            # Calculate success rate
            success = result.get('status') == 'success'
            total_tasks = len(self.task_history) + 1
            self.performance_metrics['success_rate'] = (
                (self.performance_metrics['success_rate'] * (total_tasks - 1) + (1 if success else 0)) / total_tasks
            )

            # Add to task history
            self.task_history.append({
                'task_name': task_name,
                'task_params': task_params,
                'result_status': result.get('status'),
                'timestamp': datetime.now().isoformat()
            })

            # Log task completion
            self._log_activity('task_completed', {
                'task_name': task_name,
                'result': result
            })

            # Update agent status
            self.status = 'ready'

            return result

        except Exception as e:
            # Handle any exceptions during task execution
            error_message = str(e)

            result = {
                'status': 'error',
                'message': f"Error executing task: {error_message}",
                'task_name': task_name
            }

            # Log task error
            self._log_activity('task_error', {
                'task_name': task_name,
                'error': error_message
            })

            # Update agent status
            self.status = 'error'

            return result

    def _default_task_execution(self, task_name, task_params):
        """
        Default implementation for task execution.
        This method will be called if the agent doesn't have a specific method for the task.

        Args:
            task_name (str): Name of the task to execute
            task_params (dict): Parameters for the task

        Returns:
            dict: Result of the task execution
        """
        return {
            'status': 'success',
            'message': f"Executed task {task_name} with default implementation",
            'task_name': task_name,
            'task_params': task_params
        }

    def get_status(self):
        """
        Get the current status of the agent.

        Returns:
            dict: Status information
        """
        return {
            'agent_id': self.agent_id,
            'agent_type': self.agent_type,
            'status': self.status,
            'capabilities': self.capabilities,
            'performance_metrics': self.performance_metrics,
            'last_activity': self.last_activity
        }

    def update_capabilities(self, new_capabilities):
        """
        Update the agent's capabilities.

        Args:
            new_capabilities (list): New capabilities to add

        Returns:
            dict: Updated capabilities
        """
        # Add new capabilities
        for capability in new_capabilities:
            if capability not in self.capabilities:
                self.capabilities.append(capability)

        # Log capability update
        self._log_activity('capabilities_updated', {
            'capabilities': self.capabilities
        })

        return {
            'status': 'success',
            'message': 'Capabilities updated successfully',
            'capabilities': self.capabilities
        }

    def _log_activity(self, activity_type, data):
        """
        Log agent activities for monitoring and debugging.

        Args:
            activity_type (str): Type of activity
            data (dict): Activity data
        """
        log_entry = {
            "timestamp": datetime.now().isoformat(),
            "agent_id": self.agent_id,
            "agent_type": self.agent_type,
            "activity_type": activity_type,
            "data": data
        }

        # Update last activity timestamp
        self.last_activity = log_entry["timestamp"]

        # Write to log file
        log_file = f"logs/agents/{self.agent_id}_{datetime.now().strftime('%Y%m%d')}.log"
        with open(log_file, 'a') as f:
            f.write(json.dumps(log_entry) + "\n")
