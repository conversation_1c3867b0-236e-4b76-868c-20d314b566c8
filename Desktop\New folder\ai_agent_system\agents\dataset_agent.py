"""
Dataset Agent Module

This module provides an agent that can work with datasets.
"""

import logging
from typing import Dict, List, Any, Optional

from ai_agent_system.core.base_agent import BaseAgent
from ai_agent_system.datasets.dataset_manager import DatasetManager

# Configure logging
logger = logging.getLogger(__name__)

class DatasetAgent(BaseAgent):
    """
    Agent that can work with datasets.
    """

    def __init__(self, agent_id: Optional[str] = None, capabilities: Optional[List[str]] = None, **kwargs):
        """
        Initialize the dataset agent.

        Args:
            agent_id (str, optional): Unique identifier for the agent
            capabilities (list, optional): List of capabilities for the agent
            **kwargs: Additional arguments to pass to the parent constructor
        """
        # Set default capabilities if not provided
        if capabilities is None:
            capabilities = [
                'list_datasets',
                'get_dataset',
                'query_dataset',
                'list_providers',
                'data_analysis',
                'data_visualization'
            ]

        # Initialize the parent class
        super().__init__(agent_id=agent_id, capabilities=capabilities, **kwargs)

        # Initialize dataset manager
        self.dataset_manager = DatasetManager()

        logger.info(f"Dataset Agent initialized")

    def list_providers(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """
        List available dataset providers.

        Args:
            params (dict): Parameters for listing providers

        Returns:
            dict: List of providers
        """
        try:
            # Get available providers
            providers = self.dataset_manager.get_available_providers()

            return {
                'status': 'success',
                'providers': providers,
                'count': len(providers)
            }

        except Exception as e:
            logger.error(f"Error listing providers: {e}")
            return {
                'status': 'error',
                'message': f"Error listing providers: {str(e)}"
            }

    def list_datasets(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """
        List available datasets from a provider.

        Args:
            params (dict): Parameters for listing datasets
                - provider (str): The dataset provider to use
                - query (str, optional): Query to search for datasets
                - limit (int, optional): Maximum number of datasets to return

        Returns:
            dict: List of datasets and metadata
        """
        # Extract parameters
        provider = params.get('provider')
        query = params.get('query')
        limit = params.get('limit', 10)

        if not provider:
            return {
                'status': 'error',
                'message': "Provider is required for listing datasets"
            }

        try:
            # List datasets using the dataset manager
            result = self.dataset_manager.list_datasets(provider, query, limit)

            return result

        except Exception as e:
            logger.error(f"Error listing datasets: {e}")
            return {
                'status': 'error',
                'message': f"Error listing datasets: {str(e)}"
            }

    def get_dataset(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """
        Get a dataset from a provider.

        Args:
            params (dict): Parameters for getting a dataset
                - provider (str): The dataset provider to use
                - dataset_id (str): ID of the dataset

        Returns:
            dict: The dataset and metadata
        """
        # Extract parameters
        provider = params.get('provider')
        dataset_id = params.get('dataset_id')

        if not provider:
            return {
                'status': 'error',
                'message': "Provider is required for getting a dataset"
            }

        if not dataset_id:
            return {
                'status': 'error',
                'message': "Dataset ID is required for getting a dataset"
            }

        try:
            # Get dataset using the dataset manager
            result = self.dataset_manager.get_dataset(provider, dataset_id)

            return result

        except Exception as e:
            logger.error(f"Error getting dataset: {e}")
            return {
                'status': 'error',
                'message': f"Error getting dataset: {str(e)}"
            }

    def query_dataset(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """
        Query a dataset from a provider.

        Args:
            params (dict): Parameters for querying a dataset
                - provider (str): The dataset provider to use
                - dataset_id (str): ID of the dataset
                - query (str): Query to run on the dataset

        Returns:
            dict: Query results and metadata
        """
        # Extract parameters
        provider = params.get('provider')
        dataset_id = params.get('dataset_id')
        query = params.get('query')

        if not provider:
            return {
                'status': 'error',
                'message': "Provider is required for querying a dataset"
            }

        if not dataset_id:
            return {
                'status': 'error',
                'message': "Dataset ID is required for querying a dataset"
            }

        if not query:
            return {
                'status': 'error',
                'message': "Query is required for querying a dataset"
            }

        try:
            # Query dataset using the dataset manager
            result = self.dataset_manager.query_dataset(provider, dataset_id, query)

            return result

        except Exception as e:
            logger.error(f"Error querying dataset: {e}")
            return {
                'status': 'error',
                'message': f"Error querying dataset: {str(e)}"
            }

    def data_analysis(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """
        Analyze data from a dataset.

        Args:
            params (dict): Parameters for data analysis
                - provider (str): The dataset provider to use
                - dataset_id (str): ID of the dataset
                - query (str, optional): Query to run on the dataset
                - analysis_type (str): Type of analysis to perform

        Returns:
            dict: Analysis results and metadata
        """
        # Extract parameters
        provider = params.get('provider')
        dataset_id = params.get('dataset_id')
        query = params.get('query')
        analysis_type = params.get('analysis_type', 'summary')

        if not provider:
            return {
                'status': 'error',
                'message': "Provider is required for data analysis"
            }

        if not dataset_id:
            return {
                'status': 'error',
                'message': "Dataset ID is required for data analysis"
            }

        try:
            # Get the dataset
            dataset_result = self.dataset_manager.get_dataset(provider, dataset_id)

            if dataset_result['status'] != 'success':
                return dataset_result

            # Query the dataset if a query is provided
            if query:
                data_result = self.dataset_manager.query_dataset(provider, dataset_id, query)
            else:
                # Use a default query to get all data
                data_result = self.dataset_manager.query_dataset(provider, dataset_id, "")

            if data_result['status'] != 'success':
                return data_result

            # Perform the analysis
            if analysis_type == 'summary':
                # Perform summary analysis
                import pandas as pd

                # Convert results to DataFrame
                df = pd.DataFrame(data_result['results'])

                # Calculate summary statistics
                summary = {
                    'row_count': len(df),
                    'column_count': len(df.columns),
                    'columns': list(df.columns),
                    'numeric_columns': list(df.select_dtypes(include=['number']).columns),
                    'categorical_columns': list(df.select_dtypes(include=['object']).columns),
                    'missing_values': df.isnull().sum().to_dict(),
                    'descriptive_stats': df.describe().to_dict()
                }

                return {
                    'status': 'success',
                    'analysis_type': 'summary',
                    'summary': summary
                }

            elif analysis_type == 'correlation':
                # Perform correlation analysis
                import pandas as pd

                # Convert results to DataFrame
                df = pd.DataFrame(data_result['results'])

                # Calculate correlation matrix
                numeric_df = df.select_dtypes(include=['number'])
                correlation = numeric_df.corr().to_dict()

                return {
                    'status': 'success',
                    'analysis_type': 'correlation',
                    'correlation': correlation,
                    'numeric_columns': list(numeric_df.columns)
                }

            else:
                return {
                    'status': 'error',
                    'message': f"Unsupported analysis type: {analysis_type}"
                }

        except Exception as e:
            logger.error(f"Error analyzing data: {e}")
            return {
                'status': 'error',
                'message': f"Error analyzing data: {str(e)}"
            }

    def data_visualization(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """
        Create a visualization of data from a dataset.

        Args:
            params (dict): Parameters for data visualization
                - provider (str): The dataset provider to use
                - dataset_id (str): ID of the dataset
                - query (str, optional): Query to run on the dataset
                - visualization_type (str): Type of visualization to create
                - x_column (str): Column to use for x-axis
                - y_column (str, optional): Column to use for y-axis
                - group_by (str, optional): Column to group by
                - title (str, optional): Title for the visualization
                - output_path (str, optional): Path to save the visualization

        Returns:
            dict: Visualization results and metadata
        """
        # Extract parameters
        provider = params.get('provider')
        dataset_id = params.get('dataset_id')
        query = params.get('query')
        visualization_type = params.get('visualization_type')
        x_column = params.get('x_column')
        y_column = params.get('y_column')
        group_by = params.get('group_by')
        title = params.get('title', 'Data Visualization')
        output_path = params.get('output_path', 'visualization.png')

        if not provider:
            return {
                'status': 'error',
                'message': "Provider is required for data visualization"
            }

        if not dataset_id:
            return {
                'status': 'error',
                'message': "Dataset ID is required for data visualization"
            }

        if not visualization_type:
            return {
                'status': 'error',
                'message': "Visualization type is required for data visualization"
            }

        if not x_column:
            return {
                'status': 'error',
                'message': "X column is required for data visualization"
            }

        try:
            # Query the dataset
            if query:
                data_result = self.dataset_manager.query_dataset(provider, dataset_id, query)
            else:
                # Use a default query to get all data
                data_result = self.dataset_manager.query_dataset(provider, dataset_id, "")

            if data_result['status'] != 'success':
                return data_result

            # Create the visualization
            import pandas as pd
            import matplotlib.pyplot as plt
            import seaborn as sns

            # Convert results to DataFrame
            df = pd.DataFrame(data_result['results'])

            # Set up the figure
            plt.figure(figsize=(10, 6))

            # Create the visualization based on the type
            if visualization_type == 'bar':
                # Create a bar chart
                if y_column:
                    if group_by:
                        # Grouped bar chart
                        grouped_df = df.groupby([x_column, group_by])[y_column].mean().unstack()
                        grouped_df.plot(kind='bar', ax=plt.gca())
                    else:
                        # Simple bar chart
                        sns.barplot(x=x_column, y=y_column, data=df)
                else:
                    # Count plot
                    sns.countplot(x=x_column, data=df)

            elif visualization_type == 'line':
                # Create a line chart
                if y_column:
                    if group_by:
                        # Grouped line chart
                        for group in df[group_by].unique():
                            group_df = df[df[group_by] == group]
                            plt.plot(group_df[x_column], group_df[y_column], label=group)
                        plt.legend()
                    else:
                        # Simple line chart
                        plt.plot(df[x_column], df[y_column])
                else:
                    return {
                        'status': 'error',
                        'message': "Y column is required for line charts"
                    }

            elif visualization_type == 'scatter':
                # Create a scatter plot
                if y_column:
                    if group_by:
                        # Grouped scatter plot
                        for group in df[group_by].unique():
                            group_df = df[df[group_by] == group]
                            plt.scatter(group_df[x_column], group_df[y_column], label=group)
                        plt.legend()
                    else:
                        # Simple scatter plot
                        plt.scatter(df[x_column], df[y_column])
                else:
                    return {
                        'status': 'error',
                        'message': "Y column is required for scatter plots"
                    }

            elif visualization_type == 'histogram':
                # Create a histogram
                sns.histplot(df[x_column], kde=True)

            elif visualization_type == 'box':
                # Create a box plot
                if y_column:
                    sns.boxplot(x=x_column, y=y_column, data=df)
                else:
                    sns.boxplot(x=x_column, data=df)

            else:
                return {
                    'status': 'error',
                    'message': f"Unsupported visualization type: {visualization_type}"
                }

            # Set title and labels
            plt.title(title)
            plt.xlabel(x_column)
            if y_column:
                plt.ylabel(y_column)

            # Save the visualization
            plt.tight_layout()
            plt.savefig(output_path)

            return {
                'status': 'success',
                'visualization_type': visualization_type,
                'output_path': output_path,
                'title': title,
                'x_column': x_column,
                'y_column': y_column,
                'group_by': group_by
            }

        except Exception as e:
            logger.error(f"Error creating visualization: {e}")
            return {
                'status': 'error',
                'message': f"Error creating visualization: {str(e)}"
            }
