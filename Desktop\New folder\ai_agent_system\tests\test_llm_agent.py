"""
Tests for the LLMAgent class.
"""

import os
import unittest
from unittest.mock import patch, MagicMock

from ai_agent_system.agents.llm_agent import LLMAgent

class TestLLMAgent(unittest.TestCase):
    """Tests for the LLMAgent class."""
    
    def setUp(self):
        """Set up test fixtures."""
        # Create logs directory if it doesn't exist
        os.makedirs('logs/agents', exist_ok=True)
        
        # Patch the LLMManager
        self.llm_manager_patcher = patch('ai_agent_system.agents.llm_agent.LLMManager')
        self.mock_llm_manager_class = self.llm_manager_patcher.start()
        self.mock_llm_manager = MagicMock()
        self.mock_llm_manager_class.return_value = self.mock_llm_manager
        
        # Set up the mock LLM manager
        self.mock_llm_manager.default_provider_name = 'openai'
        
        # Create an LLM agent
        self.agent = LLMAgent(agent_id='test_agent')
    
    def tearDown(self):
        """Tear down test fixtures."""
        self.llm_manager_patcher.stop()
    
    def test_init(self):
        """Test initialization of the LLMAgent class."""
        self.assertEqual(self.agent.agent_id, 'test_agent')
        self.assertEqual(self.agent.agent_type, 'llm')
        self.assertEqual(self.agent.capabilities, [
            'text_generation',
            'code_generation',
            'agent_code_generation',
            'summarization',
            'translation',
            'question_answering'
        ])
        self.assertEqual(self.agent.default_provider, 'openai')
        self.assertEqual(self.agent.llm_manager, self.mock_llm_manager)
    
    def test_text_generation(self):
        """Test the text_generation method."""
        # Set up the mock LLM manager to return a success result
        self.mock_llm_manager.generate_text.return_value = {
            'status': 'success',
            'text': 'Generated text',
            'model': 'gpt-4',
            'usage': {'prompt_tokens': 10, 'completion_tokens': 20, 'total_tokens': 30}
        }
        
        # Call the text_generation method
        result = self.agent.text_generation({
            'prompt': 'Generate some text',
            'provider': 'openai',
            'options': {'temperature': 0.7}
        })
        
        # Check that the LLM manager was called
        self.mock_llm_manager.generate_text.assert_called_once_with('Generate some text', 'openai', {'temperature': 0.7})
        
        # Check the result
        self.assertEqual(result['status'], 'success')
        self.assertEqual(result['text'], 'Generated text')
        self.assertEqual(result['model'], 'gpt-4')
        self.assertEqual(result['usage'], {'prompt_tokens': 10, 'completion_tokens': 20, 'total_tokens': 30})
    
    def test_text_generation_without_prompt(self):
        """Test the text_generation method without a prompt."""
        # Call the text_generation method without a prompt
        result = self.agent.text_generation({})
        
        # Check that the LLM manager was not called
        self.mock_llm_manager.generate_text.assert_not_called()
        
        # Check the result
        self.assertEqual(result['status'], 'error')
        self.assertEqual(result['message'], 'Prompt is required for text generation')
    
    def test_text_generation_with_error(self):
        """Test the text_generation method with an error."""
        # Set up the mock LLM manager to raise an error
        self.mock_llm_manager.generate_text.side_effect = ValueError('Test error')
        
        # Call the text_generation method
        result = self.agent.text_generation({
            'prompt': 'Generate some text'
        })
        
        # Check that the LLM manager was called
        self.mock_llm_manager.generate_text.assert_called_once()
        
        # Check the result
        self.assertEqual(result['status'], 'error')
        self.assertEqual(result['message'], 'Error generating text: Test error')
    
    def test_code_generation(self):
        """Test the code_generation method."""
        # Set up the mock LLM manager to return a success result
        self.mock_llm_manager.generate_code.return_value = {
            'status': 'success',
            'code': 'def hello_world():\n    print("Hello, world!")',
            'language': 'python',
            'model': 'gpt-4',
            'usage': {'prompt_tokens': 10, 'completion_tokens': 20, 'total_tokens': 30}
        }
        
        # Call the code_generation method
        result = self.agent.code_generation({
            'prompt': 'Generate a hello world function',
            'language': 'python',
            'provider': 'openai',
            'options': {'temperature': 0.2}
        })
        
        # Check that the LLM manager was called
        self.mock_llm_manager.generate_code.assert_called_once_with('Generate a hello world function', 'python', 'openai', {'temperature': 0.2})
        
        # Check the result
        self.assertEqual(result['status'], 'success')
        self.assertEqual(result['code'], 'def hello_world():\n    print("Hello, world!")')
        self.assertEqual(result['language'], 'python')
        self.assertEqual(result['model'], 'gpt-4')
        self.assertEqual(result['usage'], {'prompt_tokens': 10, 'completion_tokens': 20, 'total_tokens': 30})
    
    def test_code_generation_without_prompt(self):
        """Test the code_generation method without a prompt."""
        # Call the code_generation method without a prompt
        result = self.agent.code_generation({})
        
        # Check that the LLM manager was not called
        self.mock_llm_manager.generate_code.assert_not_called()
        
        # Check the result
        self.assertEqual(result['status'], 'error')
        self.assertEqual(result['message'], 'Prompt is required for code generation')
    
    def test_code_generation_with_error(self):
        """Test the code_generation method with an error."""
        # Set up the mock LLM manager to raise an error
        self.mock_llm_manager.generate_code.side_effect = ValueError('Test error')
        
        # Call the code_generation method
        result = self.agent.code_generation({
            'prompt': 'Generate a hello world function',
            'language': 'python'
        })
        
        # Check that the LLM manager was called
        self.mock_llm_manager.generate_code.assert_called_once()
        
        # Check the result
        self.assertEqual(result['status'], 'error')
        self.assertEqual(result['message'], 'Error generating code: Test error')
    
    def test_agent_code_generation(self):
        """Test the agent_code_generation method."""
        # Set up the mock LLM manager to return a success result
        self.mock_llm_manager.generate_agent_code.return_value = {
            'status': 'success',
            'agent_code': 'class TestAgent(BaseAgent):\n    pass',
            'agent_type': 'test',
            'capabilities': ['test_capability'],
            'model': 'gpt-4',
            'usage': {'prompt_tokens': 10, 'completion_tokens': 20, 'total_tokens': 30}
        }
        
        # Call the agent_code_generation method
        result = self.agent.agent_code_generation({
            'agent_type': 'test',
            'capabilities': ['test_capability'],
            'requirements': {'dependency': 'requests'},
            'provider': 'openai',
            'options': {'temperature': 0.2}
        })
        
        # Check that the LLM manager was called
        self.mock_llm_manager.generate_agent_code.assert_called_once_with('test', ['test_capability'], {'dependency': 'requests'}, 'openai', {'temperature': 0.2})
        
        # Check the result
        self.assertEqual(result['status'], 'success')
        self.assertEqual(result['agent_code'], 'class TestAgent(BaseAgent):\n    pass')
        self.assertEqual(result['agent_type'], 'test')
        self.assertEqual(result['capabilities'], ['test_capability'])
        self.assertEqual(result['model'], 'gpt-4')
        self.assertEqual(result['usage'], {'prompt_tokens': 10, 'completion_tokens': 20, 'total_tokens': 30})
    
    def test_agent_code_generation_without_agent_type(self):
        """Test the agent_code_generation method without an agent type."""
        # Call the agent_code_generation method without an agent type
        result = self.agent.agent_code_generation({
            'capabilities': ['test_capability']
        })
        
        # Check that the LLM manager was not called
        self.mock_llm_manager.generate_agent_code.assert_not_called()
        
        # Check the result
        self.assertEqual(result['status'], 'error')
        self.assertEqual(result['message'], 'Agent type is required for agent code generation')
    
    def test_agent_code_generation_without_capabilities(self):
        """Test the agent_code_generation method without capabilities."""
        # Call the agent_code_generation method without capabilities
        result = self.agent.agent_code_generation({
            'agent_type': 'test'
        })
        
        # Check that the LLM manager was not called
        self.mock_llm_manager.generate_agent_code.assert_not_called()
        
        # Check the result
        self.assertEqual(result['status'], 'error')
        self.assertEqual(result['message'], 'At least one capability is required for agent code generation')
    
    def test_agent_code_generation_with_error(self):
        """Test the agent_code_generation method with an error."""
        # Set up the mock LLM manager to raise an error
        self.mock_llm_manager.generate_agent_code.side_effect = ValueError('Test error')
        
        # Call the agent_code_generation method
        result = self.agent.agent_code_generation({
            'agent_type': 'test',
            'capabilities': ['test_capability']
        })
        
        # Check that the LLM manager was called
        self.mock_llm_manager.generate_agent_code.assert_called_once()
        
        # Check the result
        self.assertEqual(result['status'], 'error')
        self.assertEqual(result['message'], 'Error generating agent code: Test error')
    
    def test_summarization(self):
        """Test the summarization method."""
        # Set up the mock LLM manager to return a success result
        self.mock_llm_manager.generate_text.return_value = {
            'status': 'success',
            'text': 'This is a summary.',
            'model': 'gpt-4',
            'usage': {'prompt_tokens': 10, 'completion_tokens': 20, 'total_tokens': 30}
        }
        
        # Call the summarization method
        result = self.agent.summarization({
            'text': 'This is a long text that needs to be summarized.',
            'max_length': 100,
            'provider': 'openai',
            'options': {'temperature': 0.7}
        })
        
        # Check that the LLM manager was called
        self.mock_llm_manager.generate_text.assert_called_once()
        
        # Check the prompt
        prompt = self.mock_llm_manager.generate_text.call_args[0][0]
        self.assertIn('Summarize the following text in 100 words or less', prompt)
        self.assertIn('This is a long text that needs to be summarized.', prompt)
        
        # Check the provider and options
        self.assertEqual(self.mock_llm_manager.generate_text.call_args[0][1], 'openai')
        self.assertEqual(self.mock_llm_manager.generate_text.call_args[0][2], {'temperature': 0.7})
        
        # Check the result
        self.assertEqual(result['status'], 'success')
        self.assertEqual(result['summary'], 'This is a summary.')
        self.assertEqual(result['model'], 'gpt-4')
        self.assertEqual(result['usage'], {'prompt_tokens': 10, 'completion_tokens': 20, 'total_tokens': 30})
    
    def test_summarization_without_text(self):
        """Test the summarization method without text."""
        # Call the summarization method without text
        result = self.agent.summarization({})
        
        # Check that the LLM manager was not called
        self.mock_llm_manager.generate_text.assert_not_called()
        
        # Check the result
        self.assertEqual(result['status'], 'error')
        self.assertEqual(result['message'], 'Text is required for summarization')
    
    def test_summarization_with_error(self):
        """Test the summarization method with an error."""
        # Set up the mock LLM manager to raise an error
        self.mock_llm_manager.generate_text.side_effect = ValueError('Test error')
        
        # Call the summarization method
        result = self.agent.summarization({
            'text': 'This is a long text that needs to be summarized.'
        })
        
        # Check that the LLM manager was called
        self.mock_llm_manager.generate_text.assert_called_once()
        
        # Check the result
        self.assertEqual(result['status'], 'error')
        self.assertEqual(result['message'], 'Error summarizing text: Test error')
    
    def test_translation(self):
        """Test the translation method."""
        # Set up the mock LLM manager to return a success result
        self.mock_llm_manager.generate_text.return_value = {
            'status': 'success',
            'text': 'Bonjour le monde',
            'model': 'gpt-4',
            'usage': {'prompt_tokens': 10, 'completion_tokens': 20, 'total_tokens': 30}
        }
        
        # Call the translation method
        result = self.agent.translation({
            'text': 'Hello world',
            'source_language': 'English',
            'target_language': 'French',
            'provider': 'openai',
            'options': {'temperature': 0.7}
        })
        
        # Check that the LLM manager was called
        self.mock_llm_manager.generate_text.assert_called_once()
        
        # Check the prompt
        prompt = self.mock_llm_manager.generate_text.call_args[0][0]
        self.assertIn('Translate the following English text to French', prompt)
        self.assertIn('Hello world', prompt)
        
        # Check the provider and options
        self.assertEqual(self.mock_llm_manager.generate_text.call_args[0][1], 'openai')
        self.assertEqual(self.mock_llm_manager.generate_text.call_args[0][2], {'temperature': 0.7})
        
        # Check the result
        self.assertEqual(result['status'], 'success')
        self.assertEqual(result['translation'], 'Bonjour le monde')
        self.assertEqual(result['source_language'], 'English')
        self.assertEqual(result['target_language'], 'French')
        self.assertEqual(result['model'], 'gpt-4')
        self.assertEqual(result['usage'], {'prompt_tokens': 10, 'completion_tokens': 20, 'total_tokens': 30})
    
    def test_translation_without_source_language(self):
        """Test the translation method without a source language."""
        # Set up the mock LLM manager to return a success result
        self.mock_llm_manager.generate_text.return_value = {
            'status': 'success',
            'text': 'Bonjour le monde',
            'model': 'gpt-4',
            'usage': {'prompt_tokens': 10, 'completion_tokens': 20, 'total_tokens': 30}
        }
        
        # Call the translation method without a source language
        result = self.agent.translation({
            'text': 'Hello world',
            'target_language': 'French'
        })
        
        # Check that the LLM manager was called
        self.mock_llm_manager.generate_text.assert_called_once()
        
        # Check the prompt
        prompt = self.mock_llm_manager.generate_text.call_args[0][0]
        self.assertIn('Translate the following text to French', prompt)
        self.assertIn('Hello world', prompt)
        
        # Check the result
        self.assertEqual(result['status'], 'success')
        self.assertEqual(result['translation'], 'Bonjour le monde')
        self.assertIsNone(result['source_language'])
        self.assertEqual(result['target_language'], 'French')
    
    def test_translation_without_text(self):
        """Test the translation method without text."""
        # Call the translation method without text
        result = self.agent.translation({
            'target_language': 'French'
        })
        
        # Check that the LLM manager was not called
        self.mock_llm_manager.generate_text.assert_not_called()
        
        # Check the result
        self.assertEqual(result['status'], 'error')
        self.assertEqual(result['message'], 'Text is required for translation')
    
    def test_translation_without_target_language(self):
        """Test the translation method without a target language."""
        # Call the translation method without a target language
        result = self.agent.translation({
            'text': 'Hello world'
        })
        
        # Check that the LLM manager was not called
        self.mock_llm_manager.generate_text.assert_not_called()
        
        # Check the result
        self.assertEqual(result['status'], 'error')
        self.assertEqual(result['message'], 'Target language is required for translation')
    
    def test_translation_with_error(self):
        """Test the translation method with an error."""
        # Set up the mock LLM manager to raise an error
        self.mock_llm_manager.generate_text.side_effect = ValueError('Test error')
        
        # Call the translation method
        result = self.agent.translation({
            'text': 'Hello world',
            'target_language': 'French'
        })
        
        # Check that the LLM manager was called
        self.mock_llm_manager.generate_text.assert_called_once()
        
        # Check the result
        self.assertEqual(result['status'], 'error')
        self.assertEqual(result['message'], 'Error translating text: Test error')
    
    def test_question_answering(self):
        """Test the question_answering method."""
        # Set up the mock LLM manager to return a success result
        self.mock_llm_manager.generate_text.return_value = {
            'status': 'success',
            'text': 'The answer is 42.',
            'model': 'gpt-4',
            'usage': {'prompt_tokens': 10, 'completion_tokens': 20, 'total_tokens': 30}
        }
        
        # Call the question_answering method
        result = self.agent.question_answering({
            'question': 'What is the answer to life, the universe, and everything?',
            'context': 'According to The Hitchhiker\'s Guide to the Galaxy, the answer is 42.',
            'provider': 'openai',
            'options': {'temperature': 0.7}
        })
        
        # Check that the LLM manager was called
        self.mock_llm_manager.generate_text.assert_called_once()
        
        # Check the prompt
        prompt = self.mock_llm_manager.generate_text.call_args[0][0]
        self.assertIn('Answer the following question based on the provided context', prompt)
        self.assertIn('Context: According to The Hitchhiker\'s Guide to the Galaxy, the answer is 42.', prompt)
        self.assertIn('Question: What is the answer to life, the universe, and everything?', prompt)
        
        # Check the provider and options
        self.assertEqual(self.mock_llm_manager.generate_text.call_args[0][1], 'openai')
        self.assertEqual(self.mock_llm_manager.generate_text.call_args[0][2], {'temperature': 0.7})
        
        # Check the result
        self.assertEqual(result['status'], 'success')
        self.assertEqual(result['answer'], 'The answer is 42.')
        self.assertEqual(result['model'], 'gpt-4')
        self.assertEqual(result['usage'], {'prompt_tokens': 10, 'completion_tokens': 20, 'total_tokens': 30})
    
    def test_question_answering_without_context(self):
        """Test the question_answering method without context."""
        # Set up the mock LLM manager to return a success result
        self.mock_llm_manager.generate_text.return_value = {
            'status': 'success',
            'text': 'The answer is 42.',
            'model': 'gpt-4',
            'usage': {'prompt_tokens': 10, 'completion_tokens': 20, 'total_tokens': 30}
        }
        
        # Call the question_answering method without context
        result = self.agent.question_answering({
            'question': 'What is the answer to life, the universe, and everything?'
        })
        
        # Check that the LLM manager was called
        self.mock_llm_manager.generate_text.assert_called_once()
        
        # Check the prompt
        prompt = self.mock_llm_manager.generate_text.call_args[0][0]
        self.assertIn('Answer the following question', prompt)
        self.assertIn('What is the answer to life, the universe, and everything?', prompt)
        self.assertNotIn('Context:', prompt)
        
        # Check the result
        self.assertEqual(result['status'], 'success')
        self.assertEqual(result['answer'], 'The answer is 42.')
    
    def test_question_answering_without_question(self):
        """Test the question_answering method without a question."""
        # Call the question_answering method without a question
        result = self.agent.question_answering({})
        
        # Check that the LLM manager was not called
        self.mock_llm_manager.generate_text.assert_not_called()
        
        # Check the result
        self.assertEqual(result['status'], 'error')
        self.assertEqual(result['message'], 'Question is required for question answering')
    
    def test_question_answering_with_error(self):
        """Test the question_answering method with an error."""
        # Set up the mock LLM manager to raise an error
        self.mock_llm_manager.generate_text.side_effect = ValueError('Test error')
        
        # Call the question_answering method
        result = self.agent.question_answering({
            'question': 'What is the answer to life, the universe, and everything?'
        })
        
        # Check that the LLM manager was called
        self.mock_llm_manager.generate_text.assert_called_once()
        
        # Check the result
        self.assertEqual(result['status'], 'error')
        self.assertEqual(result['message'], 'Error answering question: Test error')

if __name__ == '__main__':
    unittest.main()
