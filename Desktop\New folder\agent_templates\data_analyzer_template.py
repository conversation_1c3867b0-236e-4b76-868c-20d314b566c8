"""
Data Analyzer Agent Template

This template can be used to create data analyzer agents.
"""

from ai_agent_system.core.base_agent import BaseAgent

class DataAnalyzerAgent(BaseAgent):
    """
    Data analyzer agent for the AI agent system.
    """
    
    def __init__(self, agent_id=None, capabilities=None, **kwargs):
        """
        Initialize the data analyzer agent.
        
        Args:
            agent_id (str, optional): Unique identifier for the agent
            capabilities (list, optional): List of capabilities for the agent
            **kwargs: Additional arguments to pass to the parent constructor
        """
        # Set default capabilities if not provided
        if capabilities is None:
            capabilities = [
                'load_data',
                'analyze_data',
                'visualize_data',
                'generate_report'
            ]
        
        # Initialize the parent class
        super().__init__(agent_id=agent_id, agent_type='data_analyzer', capabilities=capabilities, **kwargs)
    
    def load_data(self, params):
        """
        Load data from a file or URL.
        
        Args:
            params (dict): Parameters for loading data
                - source (str): The file path or URL to load data from
                - format (str, optional): The format of the data (csv, json, excel)
                - options (dict, optional): Additional options for loading data
                
        Returns:
            dict: Loading result
        """
        # Extract parameters
        source = params.get('source')
        format = params.get('format', 'csv')
        options = params.get('options', {})
        
        if not source:
            return {
                'status': 'error',
                'message': "Source is required for loading data"
            }
        
        try:
            # Import pandas
            import pandas as pd
            
            # Load data based on format
            if format.lower() == 'csv':
                df = pd.read_csv(source, **options)
            elif format.lower() == 'json':
                df = pd.read_json(source, **options)
            elif format.lower() == 'excel':
                df = pd.read_excel(source, **options)
            else:
                return {
                    'status': 'error',
                    'message': f"Unsupported format: {format}"
                }
            
            # Return the data
            return {
                'status': 'success',
                'source': source,
                'format': format,
                'rows': len(df),
                'columns': list(df.columns),
                'data': df.to_dict(orient='records')
            }
            
        except ImportError:
            return {
                'status': 'error',
                'message': "Pandas library not available. Please install it with 'pip install pandas'"
            }
        except Exception as e:
            return {
                'status': 'error',
                'message': f"Error loading data: {str(e)}"
            }
    
    def analyze_data(self, params):
        """
        Analyze data.
        
        Args:
            params (dict): Parameters for analyzing data
                - data (list): List of dictionaries containing the data
                - analysis_type (str): Type of analysis to perform (summary, correlation, etc.)
                - columns (list, optional): Columns to analyze
                
        Returns:
            dict: Analysis result
        """
        # Extract parameters
        data = params.get('data')
        analysis_type = params.get('analysis_type')
        columns = params.get('columns')
        
        if not data:
            return {
                'status': 'error',
                'message': "Data is required for analysis"
            }
        
        if not analysis_type:
            return {
                'status': 'error',
                'message': "Analysis type is required"
            }
        
        try:
            # Import pandas and numpy
            import pandas as pd
            import numpy as np
            
            # Convert data to DataFrame
            df = pd.DataFrame(data)
            
            # Filter columns if specified
            if columns:
                df = df[columns]
            
            # Perform analysis based on type
            if analysis_type.lower() == 'summary':
                # Calculate summary statistics
                summary = {
                    'count': df.count().to_dict(),
                    'mean': df.mean().to_dict(),
                    'std': df.std().to_dict(),
                    'min': df.min().to_dict(),
                    'max': df.max().to_dict(),
                    'median': df.median().to_dict()
                }
                
                return {
                    'status': 'success',
                    'analysis_type': 'summary',
                    'summary': summary
                }
                
            elif analysis_type.lower() == 'correlation':
                # Calculate correlation matrix
                correlation = df.corr().to_dict()
                
                return {
                    'status': 'success',
                    'analysis_type': 'correlation',
                    'correlation': correlation
                }
                
            elif analysis_type.lower() == 'missing':
                # Calculate missing values
                missing = df.isnull().sum().to_dict()
                
                return {
                    'status': 'success',
                    'analysis_type': 'missing',
                    'missing': missing
                }
                
            else:
                return {
                    'status': 'error',
                    'message': f"Unsupported analysis type: {analysis_type}"
                }
            
        except ImportError:
            return {
                'status': 'error',
                'message': "Required libraries not available. Please install them with 'pip install pandas numpy'"
            }
        except Exception as e:
            return {
                'status': 'error',
                'message': f"Error analyzing data: {str(e)}"
            }
    
    def visualize_data(self, params):
        """
        Visualize data.
        
        Args:
            params (dict): Parameters for visualizing data
                - data (list): List of dictionaries containing the data
                - visualization_type (str): Type of visualization to create
                - x (str): Column to use for x-axis
                - y (str, optional): Column to use for y-axis
                - group_by (str, optional): Column to group by
                - title (str, optional): Title for the visualization
                - output_path (str): Path to save the visualization
                
        Returns:
            dict: Visualization result
        """
        # Extract parameters
        data = params.get('data')
        visualization_type = params.get('visualization_type')
        x = params.get('x')
        y = params.get('y')
        group_by = params.get('group_by')
        title = params.get('title', 'Data Visualization')
        output_path = params.get('output_path')
        
        if not data:
            return {
                'status': 'error',
                'message': "Data is required for visualization"
            }
        
        if not visualization_type:
            return {
                'status': 'error',
                'message': "Visualization type is required"
            }
        
        if not x:
            return {
                'status': 'error',
                'message': "X column is required for visualization"
            }
        
        if not output_path:
            return {
                'status': 'error',
                'message': "Output path is required for visualization"
            }
        
        try:
            # Import required libraries
            import pandas as pd
            import matplotlib.pyplot as plt
            import seaborn as sns
            
            # Convert data to DataFrame
            df = pd.DataFrame(data)
            
            # Create the visualization
            plt.figure(figsize=(10, 6))
            
            if visualization_type.lower() == 'bar':
                if y:
                    if group_by:
                        # Grouped bar chart
                        grouped_df = df.groupby([x, group_by])[y].mean().unstack()
                        grouped_df.plot(kind='bar', ax=plt.gca())
                    else:
                        # Simple bar chart
                        sns.barplot(x=x, y=y, data=df)
                else:
                    # Count plot
                    sns.countplot(x=x, data=df)
                    
            elif visualization_type.lower() == 'line':
                if y:
                    if group_by:
                        # Grouped line chart
                        for group in df[group_by].unique():
                            group_df = df[df[group_by] == group]
                            plt.plot(group_df[x], group_df[y], label=group)
                        plt.legend()
                    else:
                        # Simple line chart
                        plt.plot(df[x], df[y])
                else:
                    return {
                        'status': 'error',
                        'message': "Y column is required for line charts"
                    }
                    
            elif visualization_type.lower() == 'scatter':
                if y:
                    if group_by:
                        # Grouped scatter plot
                        for group in df[group_by].unique():
                            group_df = df[df[group_by] == group]
                            plt.scatter(group_df[x], group_df[y], label=group)
                        plt.legend()
                    else:
                        # Simple scatter plot
                        plt.scatter(df[x], df[y])
                else:
                    return {
                        'status': 'error',
                        'message': "Y column is required for scatter plots"
                    }
                    
            elif visualization_type.lower() == 'histogram':
                # Histogram
                sns.histplot(df[x], kde=True)
                
            elif visualization_type.lower() == 'box':
                if y:
                    # Box plot with x and y
                    sns.boxplot(x=x, y=y, data=df)
                else:
                    # Box plot with just x
                    sns.boxplot(x=x, data=df)
                    
            else:
                return {
                    'status': 'error',
                    'message': f"Unsupported visualization type: {visualization_type}"
                }
            
            # Set title and labels
            plt.title(title)
            plt.xlabel(x)
            if y:
                plt.ylabel(y)
            
            # Save the visualization
            plt.tight_layout()
            plt.savefig(output_path)
            plt.close()
            
            return {
                'status': 'success',
                'visualization_type': visualization_type,
                'output_path': output_path
            }
            
        except ImportError:
            return {
                'status': 'error',
                'message': "Required libraries not available. Please install them with 'pip install pandas matplotlib seaborn'"
            }
        except Exception as e:
            return {
                'status': 'error',
                'message': f"Error visualizing data: {str(e)}"
            }
    
    def generate_report(self, params):
        """
        Generate a report from data analysis.
        
        Args:
            params (dict): Parameters for generating a report
                - data (list): List of dictionaries containing the data
                - analyses (list): List of analyses to include in the report
                - visualizations (list): List of visualizations to include in the report
                - output_path (str): Path to save the report
                
        Returns:
            dict: Report generation result
        """
        # Extract parameters
        data = params.get('data')
        analyses = params.get('analyses', [])
        visualizations = params.get('visualizations', [])
        output_path = params.get('output_path')
        
        if not data:
            return {
                'status': 'error',
                'message': "Data is required for report generation"
            }
        
        if not output_path:
            return {
                'status': 'error',
                'message': "Output path is required for report generation"
            }
        
        try:
            # Import pandas
            import pandas as pd
            
            # Convert data to DataFrame
            df = pd.DataFrame(data)
            
            # Generate the report
            report = []
            
            # Add header
            report.append("# Data Analysis Report")
            report.append(f"Generated on: {pd.Timestamp.now().strftime('%Y-%m-%d %H:%M:%S')}")
            report.append("")
            
            # Add data summary
            report.append("## Data Summary")
            report.append(f"- Number of rows: {len(df)}")
            report.append(f"- Number of columns: {len(df.columns)}")
            report.append(f"- Columns: {', '.join(df.columns)}")
            report.append("")
            
            # Add analyses
            if analyses:
                report.append("## Analyses")
                
                for analysis in analyses:
                    analysis_type = analysis.get('analysis_type')
                    columns = analysis.get('columns')
                    
                    # Perform the analysis
                    analysis_result = self.analyze_data({
                        'data': data,
                        'analysis_type': analysis_type,
                        'columns': columns
                    })
                    
                    if analysis_result['status'] == 'success':
                        report.append(f"### {analysis_type.capitalize()} Analysis")
                        
                        if analysis_type.lower() == 'summary':
                            summary = analysis_result['summary']
                            report.append("| Statistic | " + " | ".join(summary['count'].keys()) + " |")
                            report.append("| --- | " + " | ".join(["---" for _ in summary['count'].keys()]) + " |")
                            report.append("| Count | " + " | ".join([str(v) for v in summary['count'].values()]) + " |")
                            report.append("| Mean | " + " | ".join([str(v) for v in summary['mean'].values()]) + " |")
                            report.append("| Std | " + " | ".join([str(v) for v in summary['std'].values()]) + " |")
                            report.append("| Min | " + " | ".join([str(v) for v in summary['min'].values()]) + " |")
                            report.append("| Median | " + " | ".join([str(v) for v in summary['median'].values()]) + " |")
                            report.append("| Max | " + " | ".join([str(v) for v in summary['max'].values()]) + " |")
                            
                        elif analysis_type.lower() == 'correlation':
                            correlation = analysis_result['correlation']
                            columns = list(correlation.keys())
                            
                            report.append("| Column | " + " | ".join(columns) + " |")
                            report.append("| --- | " + " | ".join(["---" for _ in columns]) + " |")
                            
                            for column in columns:
                                row = correlation[column]
                                report.append(f"| {column} | " + " | ".join([str(row.get(col, 'N/A')) for col in columns]) + " |")
                                
                        elif analysis_type.lower() == 'missing':
                            missing = analysis_result['missing']
                            
                            report.append("| Column | Missing Values |")
                            report.append("| --- | --- |")
                            
                            for column, count in missing.items():
                                report.append(f"| {column} | {count} |")
                    
                    report.append("")
            
            # Add visualizations
            if visualizations:
                report.append("## Visualizations")
                
                for i, visualization in enumerate(visualizations):
                    visualization_type = visualization.get('visualization_type')
                    x = visualization.get('x')
                    y = visualization.get('y')
                    group_by = visualization.get('group_by')
                    title = visualization.get('title', f"{visualization_type.capitalize()} of {x}")
                    
                    # Create a unique output path for this visualization
                    viz_output_path = f"{output_path.rsplit('.', 1)[0]}_viz_{i+1}.png"
                    
                    # Create the visualization
                    viz_result = self.visualize_data({
                        'data': data,
                        'visualization_type': visualization_type,
                        'x': x,
                        'y': y,
                        'group_by': group_by,
                        'title': title,
                        'output_path': viz_output_path
                    })
                    
                    if viz_result['status'] == 'success':
                        report.append(f"### {title}")
                        report.append(f"![{title}]({viz_output_path})")
                        report.append("")
            
            # Write the report to file
            with open(output_path, 'w') as f:
                f.write("\n".join(report))
            
            return {
                'status': 'success',
                'output_path': output_path,
                'message': f"Report generated and saved to {output_path}"
            }
            
        except ImportError:
            return {
                'status': 'error',
                'message': "Pandas library not available. Please install it with 'pip install pandas'"
            }
        except Exception as e:
            return {
                'status': 'error',
                'message': f"Error generating report: {str(e)}"
            }
