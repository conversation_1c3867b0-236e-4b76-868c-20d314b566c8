from flask import Blueprint, jsonify, request
from flask_jwt_extended import jwt_required, get_jwt_identity
from datetime import datetime

projects_bp = Blueprint('projects', __name__)

# In-memory project storage (replace with database in production)
projects = {
    '1': {
        'id': '1',
        'name': 'AI Agent System',
        'description': 'Building an intelligent agent system for automation',
        'status': 'active',
        'type': 'AI/ML',
        'storage_used': 1024,  # in MB
        'storage_limit': 5120,  # in MB
        'test_count': 150,
        'created_at': datetime.now().isoformat(),
        'owner': '<EMAIL>'
    }
}

@projects_bp.route('/api/projects', methods=['GET'])
@jwt_required()
def get_projects():
    try:
        current_user = get_jwt_identity()
        user_projects = {
            pid: project for pid, project in projects.items()
            if project['owner'] == current_user
        }
        return jsonify(list(user_projects.values())), 200
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@projects_bp.route('/api/projects', methods=['POST'])
@jwt_required()
def create_project():
    try:
        current_user = get_jwt_identity()
        data = request.get_json()
        
        if not data or not data.get('name'):
            return jsonify({'error': 'Project name is required'}), 400
        
        project_id = str(len(projects) + 1)
        new_project = {
            'id': project_id,
            'name': data['name'],
            'description': data.get('description', ''),
            'status': data.get('status', 'active'),
            'type': data.get('type', 'Other'),
            'storage_used': 0,
            'storage_limit': data.get('storage_limit', 5120),
            'test_count': 0,
            'created_at': datetime.now().isoformat(),
            'owner': current_user
        }
        
        projects[project_id] = new_project
        return jsonify(new_project), 201
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@projects_bp.route('/api/projects/<project_id>', methods=['GET'])
@jwt_required()
def get_project(project_id):
    try:
        current_user = get_jwt_identity()
        project = projects.get(project_id)
        
        if not project:
            return jsonify({'error': 'Project not found'}), 404
        
        if project['owner'] != current_user:
            return jsonify({'error': 'Unauthorized'}), 403
        
        return jsonify(project), 200
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@projects_bp.route('/api/projects/<project_id>', methods=['PUT'])
@jwt_required()
def update_project(project_id):
    try:
        current_user = get_jwt_identity()
        project = projects.get(project_id)
        
        if not project:
            return jsonify({'error': 'Project not found'}), 404
        
        if project['owner'] != current_user:
            return jsonify({'error': 'Unauthorized'}), 403
        
        data = request.get_json()
        if not data:
            return jsonify({'error': 'No data provided'}), 400
        
        # Update allowed fields
        allowed_fields = ['name', 'description', 'status', 'type', 'storage_limit']
        for field in allowed_fields:
            if field in data:
                project[field] = data[field]
        
        return jsonify(project), 200
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@projects_bp.route('/api/projects/<project_id>', methods=['DELETE'])
@jwt_required()
def delete_project(project_id):
    try:
        current_user = get_jwt_identity()
        project = projects.get(project_id)
        
        if not project:
            return jsonify({'error': 'Project not found'}), 404
        
        if project['owner'] != current_user:
            return jsonify({'error': 'Unauthorized'}), 403
        
        del projects[project_id]
        return jsonify({'message': 'Project deleted successfully'}), 200
    except Exception as e:
        return jsonify({'error': str(e)}), 500 