"""
Main Module

This module provides the main entry point for the AI Agent System.
"""

import os
import sys
import argparse
import logging

# Import settings
from ai_agent_system.config.settings import check_required_settings, LOG_LEVEL, LOG_FILE

# Import utilities
from ai_agent_system.utils.logging_utils import setup_logging

# Import API
from ai_agent_system.api.app import run_api

# Configure logging
logger = logging.getLogger(__name__)

def parse_arguments():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(description='AI Agent System')
    
    parser.add_argument('--log-level', type=str, default=LOG_LEVEL,
                        help='Logging level (DEBUG, INFO, WARNING, ERROR, CRITICAL)')
    
    parser.add_argument('--log-file', type=str, default=LOG_FILE,
                        help='Path to the log file')
    
    parser.add_argument('--check-settings', action='store_true',
                        help='Check if required settings are set')
    
    return parser.parse_args()

def main():
    """Main entry point for the AI Agent System."""
    # Parse command line arguments
    args = parse_arguments()
    
    # Set up logging
    setup_logging(args.log_file, args.log_level)
    
    # Check required settings
    if args.check_settings:
        if check_required_settings():
            logger.info("All required settings are set")
            return 0
        else:
            logger.error("Some required settings are missing")
            return 1
    
    # Check required settings
    if not check_required_settings():
        logger.warning("Some required settings are missing")
    
    # Run the API server
    try:
        logger.info("Starting AI Agent System")
        run_api()
        return 0
    except Exception as e:
        logger.error(f"Error running AI Agent System: {e}")
        return 1

if __name__ == '__main__':
    sys.exit(main())
