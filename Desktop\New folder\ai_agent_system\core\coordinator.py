"""
Coordinator Module

This module provides a coordinator for managing multiple agents.
"""

import os
import json
import uuid
import logging
from datetime import datetime
from typing import Dict, List, Any, Optional

from ai_agent_system.core.base_agent import BaseAgent
from ai_agent_system.core.agent_factory import AgentFactory

# Configure logging
logger = logging.getLogger(__name__)

class Coordinator:
    """
    Coordinator for managing multiple agents.
    """
    
    def __init__(self, coordinator_id: Optional[str] = None):
        """
        Initialize the coordinator.
        
        Args:
            coordinator_id (str, optional): Unique identifier for the coordinator. If not provided, a new ID will be generated.
        """
        # Generate a unique ID for this coordinator if not provided
        self.coordinator_id = coordinator_id or f"coordinator_{uuid.uuid4().hex[:8]}"
        
        # Initialize coordinator state
        self.status = 'initialized'
        self.creation_time = datetime.now().isoformat()
        self.last_activity = self.creation_time
        
        # Initialize agent factory
        self.agent_factory = AgentFactory()
        
        # Dictionary to store agents
        self.agents = {}
        
        # Dictionary to store agent groups
        self.agent_groups = {}
        
        # Dictionary to store tasks
        self.tasks = {}
        
        # Create logs directory if it doesn't exist
        os.makedirs('logs/coordinators', exist_ok=True)
        
        # Log coordinator creation
        self._log_activity('coordinator_created', {
            'coordinator_id': self.coordinator_id
        })
        
        logger.info(f"Coordinator initialized with ID: {self.coordinator_id}")
    
    def create_agent(self, agent_type: str, agent_id: Optional[str] = None, capabilities: Optional[List[str]] = None, **kwargs) -> BaseAgent:
        """
        Create an agent.
        
        Args:
            agent_type (str): Type of the agent
            agent_id (str, optional): Unique identifier for the agent
            capabilities (list, optional): List of capabilities for the agent
            **kwargs: Additional arguments to pass to the agent constructor
            
        Returns:
            BaseAgent: The created agent
        """
        # Create the agent
        agent = self.agent_factory.create_agent(agent_type, agent_id, capabilities, **kwargs)
        
        # Store the agent
        self.agents[agent.agent_id] = agent
        
        # Log agent creation
        self._log_activity('agent_created', {
            'agent_id': agent.agent_id,
            'agent_type': agent_type,
            'capabilities': capabilities
        })
        
        return agent
    
    def get_agent(self, agent_id: str) -> Optional[BaseAgent]:
        """
        Get an agent by ID.
        
        Args:
            agent_id (str): ID of the agent
            
        Returns:
            BaseAgent: The agent or None if not found
        """
        return self.agents.get(agent_id)
    
    def get_agents(self, agent_type: Optional[str] = None) -> List[BaseAgent]:
        """
        Get all agents or agents of a specific type.
        
        Args:
            agent_type (str, optional): Type of agents to get. If not provided, all agents are returned.
            
        Returns:
            list: List of agents
        """
        if agent_type is None:
            return list(self.agents.values())
        else:
            return [agent for agent in self.agents.values() if agent.agent_type == agent_type]
    
    def remove_agent(self, agent_id: str) -> bool:
        """
        Remove an agent.
        
        Args:
            agent_id (str): ID of the agent to remove
            
        Returns:
            bool: True if the agent was removed, False if it doesn't exist
        """
        if agent_id not in self.agents:
            return False
        
        # Remove the agent from all groups
        for group_id, group in self.agent_groups.items():
            if agent_id in group['agents']:
                group['agents'].remove(agent_id)
        
        # Remove the agent
        agent = self.agents.pop(agent_id)
        
        # Log agent removal
        self._log_activity('agent_removed', {
            'agent_id': agent_id,
            'agent_type': agent.agent_type
        })
        
        return True
    
    def create_agent_group(self, group_id: Optional[str] = None, name: str = 'New Group', description: str = '', agent_ids: Optional[List[str]] = None) -> Dict[str, Any]:
        """
        Create a group of agents.
        
        Args:
            group_id (str, optional): Unique identifier for the group. If not provided, a new ID will be generated.
            name (str, optional): Name of the group. Default is 'New Group'.
            description (str, optional): Description of the group. Default is an empty string.
            agent_ids (list, optional): List of agent IDs to add to the group. Default is an empty list.
            
        Returns:
            dict: The created group
        """
        # Generate a unique ID for this group if not provided
        group_id = group_id or f"group_{uuid.uuid4().hex[:8]}"
        
        # Check if the group ID already exists
        if group_id in self.agent_groups:
            raise ValueError(f"Group ID already exists: {group_id}")
        
        # Check if all agent IDs exist
        if agent_ids:
            for agent_id in agent_ids:
                if agent_id not in self.agents:
                    raise ValueError(f"Agent ID not found: {agent_id}")
        
        # Create the group
        group = {
            'group_id': group_id,
            'name': name,
            'description': description,
            'agents': agent_ids or [],
            'created_at': datetime.now().isoformat(),
            'updated_at': datetime.now().isoformat()
        }
        
        # Store the group
        self.agent_groups[group_id] = group
        
        # Log group creation
        self._log_activity('group_created', {
            'group_id': group_id,
            'name': name,
            'agent_ids': agent_ids
        })
        
        return group
    
    def get_agent_group(self, group_id: str) -> Optional[Dict[str, Any]]:
        """
        Get a group by ID.
        
        Args:
            group_id (str): ID of the group
            
        Returns:
            dict: The group or None if not found
        """
        return self.agent_groups.get(group_id)
    
    def get_agent_groups(self) -> List[Dict[str, Any]]:
        """
        Get all groups.
        
        Returns:
            list: List of groups
        """
        return list(self.agent_groups.values())
    
    def add_agent_to_group(self, group_id: str, agent_id: str) -> bool:
        """
        Add an agent to a group.
        
        Args:
            group_id (str): ID of the group
            agent_id (str): ID of the agent
            
        Returns:
            bool: True if the agent was added, False if the group or agent doesn't exist or the agent is already in the group
        """
        # Check if the group exists
        if group_id not in self.agent_groups:
            return False
        
        # Check if the agent exists
        if agent_id not in self.agents:
            return False
        
        # Check if the agent is already in the group
        if agent_id in self.agent_groups[group_id]['agents']:
            return False
        
        # Add the agent to the group
        self.agent_groups[group_id]['agents'].append(agent_id)
        self.agent_groups[group_id]['updated_at'] = datetime.now().isoformat()
        
        # Log agent addition to group
        self._log_activity('agent_added_to_group', {
            'group_id': group_id,
            'agent_id': agent_id
        })
        
        return True
    
    def remove_agent_from_group(self, group_id: str, agent_id: str) -> bool:
        """
        Remove an agent from a group.
        
        Args:
            group_id (str): ID of the group
            agent_id (str): ID of the agent
            
        Returns:
            bool: True if the agent was removed, False if the group or agent doesn't exist or the agent is not in the group
        """
        # Check if the group exists
        if group_id not in self.agent_groups:
            return False
        
        # Check if the agent is in the group
        if agent_id not in self.agent_groups[group_id]['agents']:
            return False
        
        # Remove the agent from the group
        self.agent_groups[group_id]['agents'].remove(agent_id)
        self.agent_groups[group_id]['updated_at'] = datetime.now().isoformat()
        
        # Log agent removal from group
        self._log_activity('agent_removed_from_group', {
            'group_id': group_id,
            'agent_id': agent_id
        })
        
        return True
    
    def remove_agent_group(self, group_id: str) -> bool:
        """
        Remove a group.
        
        Args:
            group_id (str): ID of the group to remove
            
        Returns:
            bool: True if the group was removed, False if it doesn't exist
        """
        if group_id not in self.agent_groups:
            return False
        
        # Remove the group
        group = self.agent_groups.pop(group_id)
        
        # Log group removal
        self._log_activity('group_removed', {
            'group_id': group_id,
            'name': group['name']
        })
        
        return True
    
    def create_task(self, task_name: str, task_description: str, agent_id: Optional[str] = None, group_id: Optional[str] = None, task_params: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        Create a task for an agent or group.
        
        Args:
            task_name (str): Name of the task
            task_description (str): Description of the task
            agent_id (str, optional): ID of the agent to assign the task to
            group_id (str, optional): ID of the group to assign the task to
            task_params (dict, optional): Parameters for the task
            
        Returns:
            dict: The created task
        """
        # Check if either agent_id or group_id is provided
        if agent_id is None and group_id is None:
            raise ValueError("Either agent_id or group_id must be provided")
        
        # Check if the agent exists
        if agent_id is not None and agent_id not in self.agents:
            raise ValueError(f"Agent ID not found: {agent_id}")
        
        # Check if the group exists
        if group_id is not None and group_id not in self.agent_groups:
            raise ValueError(f"Group ID not found: {group_id}")
        
        # Generate a unique ID for this task
        task_id = f"task_{uuid.uuid4().hex[:8]}"
        
        # Create the task
        task = {
            'task_id': task_id,
            'task_name': task_name,
            'task_description': task_description,
            'agent_id': agent_id,
            'group_id': group_id,
            'task_params': task_params or {},
            'status': 'created',
            'created_at': datetime.now().isoformat(),
            'updated_at': datetime.now().isoformat(),
            'completed_at': None,
            'result': None
        }
        
        # Store the task
        self.tasks[task_id] = task
        
        # Log task creation
        self._log_activity('task_created', {
            'task_id': task_id,
            'task_name': task_name,
            'agent_id': agent_id,
            'group_id': group_id
        })
        
        return task
    
    def get_task(self, task_id: str) -> Optional[Dict[str, Any]]:
        """
        Get a task by ID.
        
        Args:
            task_id (str): ID of the task
            
        Returns:
            dict: The task or None if not found
        """
        return self.tasks.get(task_id)
    
    def get_tasks(self, agent_id: Optional[str] = None, group_id: Optional[str] = None, status: Optional[str] = None) -> List[Dict[str, Any]]:
        """
        Get tasks.
        
        Args:
            agent_id (str, optional): ID of the agent to get tasks for
            group_id (str, optional): ID of the group to get tasks for
            status (str, optional): Status of the tasks to get
            
        Returns:
            list: List of tasks
        """
        tasks = list(self.tasks.values())
        
        # Filter by agent ID
        if agent_id is not None:
            tasks = [task for task in tasks if task['agent_id'] == agent_id]
        
        # Filter by group ID
        if group_id is not None:
            tasks = [task for task in tasks if task['group_id'] == group_id]
        
        # Filter by status
        if status is not None:
            tasks = [task for task in tasks if task['status'] == status]
        
        return tasks
    
    def execute_task(self, task_id: str) -> Dict[str, Any]:
        """
        Execute a task.
        
        Args:
            task_id (str): ID of the task to execute
            
        Returns:
            dict: The updated task
        """
        # Check if the task exists
        if task_id not in self.tasks:
            raise ValueError(f"Task ID not found: {task_id}")
        
        # Get the task
        task = self.tasks[task_id]
        
        # Check if the task is already completed
        if task['status'] == 'completed':
            return task
        
        # Update task status
        task['status'] = 'executing'
        task['updated_at'] = datetime.now().isoformat()
        
        # Log task execution start
        self._log_activity('task_execution_started', {
            'task_id': task_id,
            'task_name': task['task_name']
        })
        
        try:
            # Execute the task
            if task['agent_id'] is not None:
                # Execute the task on a single agent
                agent = self.agents[task['agent_id']]
                result = agent.execute_task(task['task_name'], task['task_params'])
            else:
                # Execute the task on a group of agents
                group = self.agent_groups[task['group_id']]
                results = {}
                
                for agent_id in group['agents']:
                    agent = self.agents[agent_id]
                    if task['task_name'] in agent.capabilities:
                        results[agent_id] = agent.execute_task(task['task_name'], task['task_params'])
                
                # Combine results
                result = {
                    'status': 'success' if all(r['status'] == 'success' for r in results.values()) else 'error',
                    'message': f"Task executed on {len(results)} agents",
                    'agent_results': results
                }
            
            # Update task with result
            task['status'] = 'completed'
            task['result'] = result
            task['completed_at'] = datetime.now().isoformat()
            task['updated_at'] = datetime.now().isoformat()
            
            # Log task execution completion
            self._log_activity('task_execution_completed', {
                'task_id': task_id,
                'task_name': task['task_name'],
                'status': result['status']
            })
            
            return task
            
        except Exception as e:
            # Handle any exceptions during task execution
            error_message = str(e)
            
            # Update task with error
            task['status'] = 'error'
            task['result'] = {
                'status': 'error',
                'message': f"Error executing task: {error_message}"
            }
            task['updated_at'] = datetime.now().isoformat()
            
            # Log task execution error
            self._log_activity('task_execution_error', {
                'task_id': task_id,
                'task_name': task['task_name'],
                'error': error_message
            })
            
            return task
    
    def cancel_task(self, task_id: str) -> Dict[str, Any]:
        """
        Cancel a task.
        
        Args:
            task_id (str): ID of the task to cancel
            
        Returns:
            dict: The updated task
        """
        # Check if the task exists
        if task_id not in self.tasks:
            raise ValueError(f"Task ID not found: {task_id}")
        
        # Get the task
        task = self.tasks[task_id]
        
        # Check if the task is already completed or cancelled
        if task['status'] in ['completed', 'cancelled']:
            return task
        
        # Update task status
        task['status'] = 'cancelled'
        task['updated_at'] = datetime.now().isoformat()
        
        # Log task cancellation
        self._log_activity('task_cancelled', {
            'task_id': task_id,
            'task_name': task['task_name']
        })
        
        return task
    
    def remove_task(self, task_id: str) -> bool:
        """
        Remove a task.
        
        Args:
            task_id (str): ID of the task to remove
            
        Returns:
            bool: True if the task was removed, False if it doesn't exist
        """
        if task_id not in self.tasks:
            return False
        
        # Remove the task
        task = self.tasks.pop(task_id)
        
        # Log task removal
        self._log_activity('task_removed', {
            'task_id': task_id,
            'task_name': task['task_name']
        })
        
        return True
    
    def get_status(self) -> Dict[str, Any]:
        """
        Get the current status of the coordinator.
        
        Returns:
            dict: Coordinator status information
        """
        return {
            'coordinator_id': self.coordinator_id,
            'status': self.status,
            'creation_time': self.creation_time,
            'last_activity': self.last_activity,
            'agent_count': len(self.agents),
            'group_count': len(self.agent_groups),
            'task_count': len(self.tasks),
            'available_agent_types': self.agent_factory.get_available_agent_types()
        }
    
    def _log_activity(self, activity_type: str, data: Optional[Dict[str, Any]] = None) -> None:
        """
        Log coordinator activity.
        
        Args:
            activity_type (str): Type of activity
            data (dict, optional): Additional activity data
        """
        timestamp = datetime.now().isoformat()
        
        log_entry = {
            'timestamp': timestamp,
            'coordinator_id': self.coordinator_id,
            'activity_type': activity_type,
            'data': data or {}
        }
        
        # Update last activity time
        self.last_activity = timestamp
        
        # Write log entry to file
        log_file = f"logs/coordinators/{self.coordinator_id}.jsonl"
        with open(log_file, 'a') as f:
            f.write(json.dumps(log_entry) + '\n')
        
        # Log to console as well
        logger.debug(f"Coordinator {self.coordinator_id} activity: {activity_type}")
    
    def __str__(self) -> str:
        """String representation of the coordinator."""
        return f"Coordinator (ID: {self.coordinator_id})"
    
    def __repr__(self) -> str:
        """Detailed string representation of the coordinator."""
        return f"Coordinator(coordinator_id='{self.coordinator_id}', agents={len(self.agents)}, groups={len(self.agent_groups)}, tasks={len(self.tasks)})"
