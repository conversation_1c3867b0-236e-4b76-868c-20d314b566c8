"""
Monitoring Utilities

This module provides utilities for monitoring the AI Agent System.
"""

import os
import time
import threading
import logging
from typing import Dict, Any, Optional

# Import Prometheus client if available
try:
    import prometheus_client
    from prometheus_client import Counter, Gauge, Histogram, Summary
    prometheus_available = True
except ImportError:
    prometheus_available = False

# Configure logging
logger = logging.getLogger(__name__)

class Monitoring:
    """
    Monitoring utilities for the AI Agent System.
    """
    
    def __init__(self, enable_prometheus: bool = True, prometheus_port: int = 9090):
        """
        Initialize the monitoring utilities.
        
        Args:
            enable_prometheus (bool, optional): Whether to enable Prometheus metrics. Default is True.
            prometheus_port (int, optional): Port to expose Prometheus metrics on. Default is 9090.
        """
        self.enable_prometheus = enable_prometheus and prometheus_available
        self.prometheus_port = prometheus_port
        
        # Initialize metrics
        self.metrics = {}
        
        # Start Prometheus server if enabled
        if self.enable_prometheus:
            self._start_prometheus_server()
            self._initialize_metrics()
            
            logger.info(f"Prometheus metrics server started on port {prometheus_port}")
        elif enable_prometheus and not prometheus_available:
            logger.warning("Prometheus client not available. Please install it with 'pip install prometheus-client'")
    
    def _start_prometheus_server(self):
        """Start the Prometheus metrics server."""
        prometheus_client.start_http_server(self.prometheus_port)
    
    def _initialize_metrics(self):
        """Initialize Prometheus metrics."""
        # Agent metrics
        self.metrics['agent_count'] = Gauge('ai_agent_system_agent_count', 'Number of agents', ['agent_type'])
        self.metrics['agent_task_count'] = Counter('ai_agent_system_agent_task_count', 'Number of tasks executed by agents', ['agent_id', 'agent_type', 'task_name', 'status'])
        self.metrics['agent_task_duration'] = Histogram('ai_agent_system_agent_task_duration', 'Duration of agent tasks in seconds', ['agent_id', 'agent_type', 'task_name'])
        
        # LLM metrics
        self.metrics['llm_request_count'] = Counter('ai_agent_system_llm_request_count', 'Number of LLM requests', ['provider', 'model', 'status'])
        self.metrics['llm_token_count'] = Counter('ai_agent_system_llm_token_count', 'Number of tokens used in LLM requests', ['provider', 'model', 'type'])
        self.metrics['llm_request_duration'] = Histogram('ai_agent_system_llm_request_duration', 'Duration of LLM requests in seconds', ['provider', 'model'])
        
        # Dataset metrics
        self.metrics['dataset_request_count'] = Counter('ai_agent_system_dataset_request_count', 'Number of dataset requests', ['provider', 'operation', 'status'])
        self.metrics['dataset_request_duration'] = Histogram('ai_agent_system_dataset_request_duration', 'Duration of dataset requests in seconds', ['provider', 'operation'])
        
        # API metrics
        self.metrics['api_request_count'] = Counter('ai_agent_system_api_request_count', 'Number of API requests', ['endpoint', 'method', 'status'])
        self.metrics['api_request_duration'] = Histogram('ai_agent_system_api_request_duration', 'Duration of API requests in seconds', ['endpoint', 'method'])
        
        # System metrics
        self.metrics['system_memory_usage'] = Gauge('ai_agent_system_memory_usage', 'Memory usage in bytes')
        self.metrics['system_cpu_usage'] = Gauge('ai_agent_system_cpu_usage', 'CPU usage percentage')
        
        # Start system metrics collection
        self._start_system_metrics_collection()
    
    def _start_system_metrics_collection(self):
        """Start collecting system metrics in a background thread."""
        if not self.enable_prometheus:
            return
        
        def collect_system_metrics():
            """Collect system metrics."""
            try:
                import psutil
                
                while True:
                    # Collect memory usage
                    memory_info = psutil.virtual_memory()
                    self.metrics['system_memory_usage'].set(memory_info.used)
                    
                    # Collect CPU usage
                    cpu_percent = psutil.cpu_percent(interval=1)
                    self.metrics['system_cpu_usage'].set(cpu_percent)
                    
                    # Sleep for a while
                    time.sleep(5)
            except ImportError:
                logger.warning("psutil not available. System metrics will not be collected.")
            except Exception as e:
                logger.error(f"Error collecting system metrics: {e}")
        
        # Start the collection thread
        thread = threading.Thread(target=collect_system_metrics, daemon=True)
        thread.start()
    
    def track_agent_task(self, agent_id: str, agent_type: str, task_name: str, status: str, duration: float):
        """
        Track an agent task.
        
        Args:
            agent_id (str): ID of the agent
            agent_type (str): Type of the agent
            task_name (str): Name of the task
            status (str): Status of the task (success, error)
            duration (float): Duration of the task in seconds
        """
        if not self.enable_prometheus:
            return
        
        try:
            # Increment task count
            self.metrics['agent_task_count'].labels(agent_id=agent_id, agent_type=agent_type, task_name=task_name, status=status).inc()
            
            # Record task duration
            self.metrics['agent_task_duration'].labels(agent_id=agent_id, agent_type=agent_type, task_name=task_name).observe(duration)
        except Exception as e:
            logger.error(f"Error tracking agent task: {e}")
    
    def track_llm_request(self, provider: str, model: str, status: str, duration: float, tokens: Optional[Dict[str, int]] = None):
        """
        Track an LLM request.
        
        Args:
            provider (str): LLM provider
            model (str): LLM model
            status (str): Status of the request (success, error)
            duration (float): Duration of the request in seconds
            tokens (dict, optional): Token usage information
        """
        if not self.enable_prometheus:
            return
        
        try:
            # Increment request count
            self.metrics['llm_request_count'].labels(provider=provider, model=model, status=status).inc()
            
            # Record request duration
            self.metrics['llm_request_duration'].labels(provider=provider, model=model).observe(duration)
            
            # Record token usage if available
            if tokens:
                for token_type, token_count in tokens.items():
                    self.metrics['llm_token_count'].labels(provider=provider, model=model, type=token_type).inc(token_count)
        except Exception as e:
            logger.error(f"Error tracking LLM request: {e}")
    
    def track_dataset_request(self, provider: str, operation: str, status: str, duration: float):
        """
        Track a dataset request.
        
        Args:
            provider (str): Dataset provider
            operation (str): Operation performed
            status (str): Status of the request (success, error)
            duration (float): Duration of the request in seconds
        """
        if not self.enable_prometheus:
            return
        
        try:
            # Increment request count
            self.metrics['dataset_request_count'].labels(provider=provider, operation=operation, status=status).inc()
            
            # Record request duration
            self.metrics['dataset_request_duration'].labels(provider=provider, operation=operation).observe(duration)
        except Exception as e:
            logger.error(f"Error tracking dataset request: {e}")
    
    def track_api_request(self, endpoint: str, method: str, status: str, duration: float):
        """
        Track an API request.
        
        Args:
            endpoint (str): API endpoint
            method (str): HTTP method
            status (str): Status of the request (success, error)
            duration (float): Duration of the request in seconds
        """
        if not self.enable_prometheus:
            return
        
        try:
            # Increment request count
            self.metrics['api_request_count'].labels(endpoint=endpoint, method=method, status=status).inc()
            
            # Record request duration
            self.metrics['api_request_duration'].labels(endpoint=endpoint, method=method).observe(duration)
        except Exception as e:
            logger.error(f"Error tracking API request: {e}")
    
    def update_agent_count(self, agent_type: str, count: int):
        """
        Update the agent count.
        
        Args:
            agent_type (str): Type of the agent
            count (int): Number of agents
        """
        if not self.enable_prometheus:
            return
        
        try:
            self.metrics['agent_count'].labels(agent_type=agent_type).set(count)
        except Exception as e:
            logger.error(f"Error updating agent count: {e}")

# Create a global instance of the monitoring utilities
monitoring = Monitoring()
