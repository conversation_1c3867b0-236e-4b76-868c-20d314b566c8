"""
Base Agent Module

This module provides the base class for all agents in the system.
"""

import os
import json
import uuid
import logging
from datetime import datetime
from typing import Dict, List, Any, Optional

# Configure logging
logger = logging.getLogger(__name__)

class BaseAgent:
    """
    Base class for all agents in the system.
    """
    
    def __init__(self, agent_id: Optional[str] = None, agent_type: str = 'base', capabilities: Optional[List[str]] = None):
        """
        Initialize the base agent.
        
        Args:
            agent_id (str, optional): Unique identifier for the agent. If not provided, a new ID will be generated.
            agent_type (str, optional): Type of the agent. Default is 'base'.
            capabilities (list, optional): List of capabilities this agent has. Default is an empty list.
        """
        # Generate a unique ID for this agent if not provided
        self.agent_id = agent_id or f"{agent_type}_agent_{uuid.uuid4().hex[:8]}"
        
        # Store agent information
        self.agent_type = agent_type
        self.capabilities = capabilities or []
        
        # Initialize agent state
        self.status = 'initialized'
        self.creation_time = datetime.now().isoformat()
        self.last_activity = self.creation_time
        
        # Initialize performance metrics
        self.performance_metrics = {
            'tasks_completed': 0,
            'success_rate': 0.0,
            'average_response_time': 0.0
        }
        
        # Initialize task history
        self.task_history = []
        
        # Create logs directory if it doesn't exist
        os.makedirs('logs/agents', exist_ok=True)
        
        # Log agent creation
        self._log_activity('agent_created', {
            'agent_id': self.agent_id,
            'agent_type': self.agent_type,
            'capabilities': self.capabilities
        })
        
        logger.info(f"Base Agent initialized with ID: {self.agent_id}")
    
    def execute_task(self, task_name: str, task_params: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        Execute a task based on the agent's capabilities.
        
        Args:
            task_name (str): Name of the task to execute
            task_params (dict, optional): Parameters for the task
            
        Returns:
            dict: Result of the task execution
        """
        # Update agent status
        self.status = 'executing_task'
        
        # Log task start
        self._log_activity('task_started', {
            'task_name': task_name,
            'task_params': task_params
        })
        
        # Check if the agent has the capability to execute this task
        if task_name not in self.capabilities:
            result = {
                'status': 'error',
                'message': f"Agent does not have capability: {task_name}",
                'task_name': task_name
            }
            
            # Log task failure
            self._log_activity('task_failed', result)
            
            # Update agent status
            self.status = 'ready'
            
            return result
        
        try:
            # Record start time
            start_time = datetime.now()
            
            # Execute the task if the agent has a method for it
            if hasattr(self, task_name) and callable(getattr(self, task_name)):
                task_method = getattr(self, task_name)
                result = task_method(task_params or {})
            else:
                # Default implementation for unknown tasks
                result = self._default_task_execution(task_name, task_params or {})
            
            # Calculate response time
            response_time = (datetime.now() - start_time).total_seconds()
            
            # Update performance metrics
            self.performance_metrics['tasks_completed'] += 1
            
            # Calculate success rate
            success = result.get('status') == 'success'
            total_tasks = len(self.task_history) + 1
            self.performance_metrics['success_rate'] = (
                (self.performance_metrics['success_rate'] * (total_tasks - 1) + (1 if success else 0)) / total_tasks
            )
            
            # Update average response time
            self.performance_metrics['average_response_time'] = (
                (self.performance_metrics['average_response_time'] * (total_tasks - 1) + response_time) / total_tasks
            )
            
            # Add to task history
            self.task_history.append({
                'task_name': task_name,
                'task_params': task_params,
                'result_status': result.get('status'),
                'response_time': response_time,
                'timestamp': datetime.now().isoformat()
            })
            
            # Log task completion
            self._log_activity('task_completed', {
                'task_name': task_name,
                'result': result,
                'response_time': response_time
            })
            
            # Update agent status
            self.status = 'ready'
            
            return result
            
        except Exception as e:
            # Handle any exceptions during task execution
            error_message = str(e)
            
            result = {
                'status': 'error',
                'message': f"Error executing task: {error_message}",
                'task_name': task_name
            }
            
            # Log task error
            self._log_activity('task_error', {
                'task_name': task_name,
                'error': error_message
            })
            
            # Update agent status
            self.status = 'error'
            
            return result
    
    def _default_task_execution(self, task_name: str, task_params: Dict[str, Any]) -> Dict[str, Any]:
        """
        Default implementation for task execution.
        This method will be called if the agent doesn't have a specific method for the task.
        
        Args:
            task_name (str): Name of the task to execute
            task_params (dict): Parameters for the task
            
        Returns:
            dict: Result of the task execution
        """
        return {
            'status': 'success',
            'message': f"Executed task {task_name} with default implementation",
            'task_name': task_name,
            'task_params': task_params
        }
    
    def get_status(self) -> Dict[str, Any]:
        """
        Get the current status of the agent.
        
        Returns:
            dict: Agent status information
        """
        return {
            'agent_id': self.agent_id,
            'agent_type': self.agent_type,
            'status': self.status,
            'capabilities': self.capabilities,
            'performance_metrics': self.performance_metrics,
            'creation_time': self.creation_time,
            'last_activity': self.last_activity,
            'tasks_completed': self.performance_metrics['tasks_completed'],
            'task_history_length': len(self.task_history)
        }
    
    def get_task_history(self, limit: int = 10) -> List[Dict[str, Any]]:
        """
        Get the task execution history.
        
        Args:
            limit (int, optional): Maximum number of history items to return. Default is 10.
            
        Returns:
            list: Task execution history
        """
        return self.task_history[-limit:] if limit > 0 else self.task_history
    
    def add_capability(self, capability: str) -> bool:
        """
        Add a new capability to the agent.
        
        Args:
            capability (str): The capability to add
            
        Returns:
            bool: True if the capability was added, False if it already exists
        """
        if capability in self.capabilities:
            return False
        
        self.capabilities.append(capability)
        
        # Log capability addition
        self._log_activity('capability_added', {
            'capability': capability
        })
        
        return True
    
    def remove_capability(self, capability: str) -> bool:
        """
        Remove a capability from the agent.
        
        Args:
            capability (str): The capability to remove
            
        Returns:
            bool: True if the capability was removed, False if it doesn't exist
        """
        if capability not in self.capabilities:
            return False
        
        self.capabilities.remove(capability)
        
        # Log capability removal
        self._log_activity('capability_removed', {
            'capability': capability
        })
        
        return True
    
    def _log_activity(self, activity_type: str, data: Optional[Dict[str, Any]] = None) -> None:
        """
        Log agent activity.
        
        Args:
            activity_type (str): Type of activity
            data (dict, optional): Additional activity data
        """
        timestamp = datetime.now().isoformat()
        
        log_entry = {
            'timestamp': timestamp,
            'agent_id': self.agent_id,
            'agent_type': self.agent_type,
            'activity_type': activity_type,
            'data': data or {}
        }
        
        # Update last activity time
        self.last_activity = timestamp
        
        # Write log entry to file
        log_file = f"logs/agents/{self.agent_id}.jsonl"
        with open(log_file, 'a') as f:
            f.write(json.dumps(log_entry) + '\n')
        
        # Log to console as well
        logger.debug(f"Agent {self.agent_id} activity: {activity_type}")
    
    def __str__(self) -> str:
        """String representation of the agent."""
        return f"{self.agent_type.capitalize()} Agent (ID: {self.agent_id})"
    
    def __repr__(self) -> str:
        """Detailed string representation of the agent."""
        return f"{self.agent_type.capitalize()}Agent(agent_id='{self.agent_id}', capabilities={self.capabilities})"
