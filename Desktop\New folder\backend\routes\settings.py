from flask import Blueprint, jsonify, request
from flask_jwt_extended import jwt_required, get_jwt_identity

settings_bp = Blueprint('settings', __name__)

# In-memory storage for settings (replace with database in production)
user_settings = {}

@settings_bp.route('/api/settings', methods=['GET'])
@jwt_required()
def get_settings():
    try:
        current_user = get_jwt_identity()
        
        # Return user's settings or default settings if none exist
        settings = user_settings.get(current_user, {
            'api_config': {
                'api_key': '',
                'webhook_url': '',
                'rate_limit': 1000
            },
            'preferences': {
                'language': 'en',
                'timezone': 'UTC',
                'theme': 'light'
            },
            'notifications': {
                'email': True,
                'push': False,
                'slack': False
            },
            'security': {
                'two_factor_auth': False,
                'session_timeout': 30
            }
        })
        
        return jsonify(settings), 200
        
    except Exception as e:
        return jsonify({'message': str(e)}), 500

@settings_bp.route('/api/settings', methods=['PUT'])
@jwt_required()
def update_settings():
    try:
        current_user = get_jwt_identity()
        data = request.get_json()
        
        # Validate required fields
        required_fields = ['api_config', 'preferences', 'notifications', 'security']
        if not all(field in data for field in required_fields):
            return jsonify({'message': 'Missing required fields'}), 400
        
        # Update user settings
        user_settings[current_user] = data
        
        return jsonify({'message': 'Settings updated successfully'}), 200
        
    except Exception as e:
        return jsonify({'message': str(e)}), 500 