"""
API Module

This module provides a Flask API for the AI Agent System.
"""

import os
import json
import logging
from flask import Flask, request, jsonify

# Import settings
from ai_agent_system.config.settings import SERVER_HOST, SERVER_PORT, DEBUG

# Import core components
from ai_agent_system.core.coordinator import Coordinator

# Configure logging
logger = logging.getLogger(__name__)

# Create Flask app
app = Flask(__name__)

# Create coordinator
coordinator = Coordinator()

# Register built-in agent types
from ai_agent_system.agents.llm_agent import LLMAgent
from ai_agent_system.agents.dataset_agent import DatasetAgent
from ai_agent_system.agents.agent_generator import AgentGenerator

coordinator.agent_factory.register_agent_class('llm', LLMAgent)
coordinator.agent_factory.register_agent_class('dataset', DatasetAgent)
coordinator.agent_factory.register_agent_class('generator', AgentGenerator)

# Create default agents
llm_agent = coordinator.create_agent('llm')
dataset_agent = coordinator.create_agent('dataset')
generator_agent = coordinator.create_agent('generator')

@app.route('/health', methods=['GET'])
def health_check():
    """Health check endpoint."""
    return jsonify({
        'status': 'success',
        'message': 'API is running',
        'coordinator': coordinator.get_status()
    })

@app.route('/api/agents', methods=['GET'])
def list_agents():
    """List all agents."""
    agent_type = request.args.get('agent_type')
    agents = coordinator.get_agents(agent_type)
    
    return jsonify({
        'status': 'success',
        'agents': [agent.get_status() for agent in agents],
        'count': len(agents)
    })

@app.route('/api/agents', methods=['POST'])
def create_agent():
    """Create a new agent."""
    data = request.json
    
    agent_type = data.get('agent_type')
    agent_id = data.get('agent_id')
    capabilities = data.get('capabilities')
    
    if not agent_type:
        return jsonify({
            'status': 'error',
            'message': 'Agent type is required'
        }), 400
    
    try:
        agent = coordinator.create_agent(agent_type, agent_id, capabilities)
        
        return jsonify({
            'status': 'success',
            'agent': agent.get_status()
        }), 201
    except Exception as e:
        logger.error(f"Error creating agent: {e}")
        return jsonify({
            'status': 'error',
            'message': str(e)
        }), 400

@app.route('/api/agents/<agent_id>', methods=['GET'])
def get_agent(agent_id):
    """Get information about a specific agent."""
    agent = coordinator.get_agent(agent_id)
    
    if not agent:
        return jsonify({
            'status': 'error',
            'message': f'Agent not found: {agent_id}'
        }), 404
    
    return jsonify({
        'status': 'success',
        'agent': agent.get_status()
    })

@app.route('/api/agents/<agent_id>/tasks', methods=['GET'])
def get_agent_tasks(agent_id):
    """Get tasks for a specific agent."""
    agent = coordinator.get_agent(agent_id)
    
    if not agent:
        return jsonify({
            'status': 'error',
            'message': f'Agent not found: {agent_id}'
        }), 404
    
    limit = request.args.get('limit', 10, type=int)
    tasks = agent.get_task_history(limit)
    
    return jsonify({
        'status': 'success',
        'tasks': tasks,
        'count': len(tasks)
    })

@app.route('/api/agents/<agent_id>/tasks', methods=['POST'])
def execute_agent_task(agent_id):
    """Execute a task on a specific agent."""
    agent = coordinator.get_agent(agent_id)
    
    if not agent:
        return jsonify({
            'status': 'error',
            'message': f'Agent not found: {agent_id}'
        }), 404
    
    data = request.json
    
    task_name = data.get('task_name')
    task_params = data.get('task_params', {})
    
    if not task_name:
        return jsonify({
            'status': 'error',
            'message': 'Task name is required'
        }), 400
    
    result = agent.execute_task(task_name, task_params)
    
    if result.get('status') == 'error':
        return jsonify(result), 400
    
    return jsonify(result)

@app.route('/api/tasks', methods=['GET'])
def list_tasks():
    """List all tasks."""
    agent_id = request.args.get('agent_id')
    group_id = request.args.get('group_id')
    status = request.args.get('status')
    
    tasks = coordinator.get_tasks(agent_id, group_id, status)
    
    return jsonify({
        'status': 'success',
        'tasks': tasks,
        'count': len(tasks)
    })

@app.route('/api/tasks', methods=['POST'])
def create_task():
    """Create a new task."""
    data = request.json
    
    task_name = data.get('task_name')
    task_description = data.get('task_description', '')
    agent_id = data.get('agent_id')
    group_id = data.get('group_id')
    task_params = data.get('task_params', {})
    
    if not task_name:
        return jsonify({
            'status': 'error',
            'message': 'Task name is required'
        }), 400
    
    if not agent_id and not group_id:
        return jsonify({
            'status': 'error',
            'message': 'Either agent_id or group_id is required'
        }), 400
    
    try:
        task = coordinator.create_task(task_name, task_description, agent_id, group_id, task_params)
        
        return jsonify({
            'status': 'success',
            'task': task
        }), 201
    except Exception as e:
        logger.error(f"Error creating task: {e}")
        return jsonify({
            'status': 'error',
            'message': str(e)
        }), 400

@app.route('/api/tasks/<task_id>', methods=['GET'])
def get_task(task_id):
    """Get information about a specific task."""
    task = coordinator.get_task(task_id)
    
    if not task:
        return jsonify({
            'status': 'error',
            'message': f'Task not found: {task_id}'
        }), 404
    
    return jsonify({
        'status': 'success',
        'task': task
    })

@app.route('/api/tasks/<task_id>/execute', methods=['POST'])
def execute_task(task_id):
    """Execute a specific task."""
    task = coordinator.get_task(task_id)
    
    if not task:
        return jsonify({
            'status': 'error',
            'message': f'Task not found: {task_id}'
        }), 404
    
    try:
        result = coordinator.execute_task(task_id)
        
        return jsonify({
            'status': 'success',
            'task': result
        })
    except Exception as e:
        logger.error(f"Error executing task: {e}")
        return jsonify({
            'status': 'error',
            'message': str(e)
        }), 400

@app.route('/api/tasks/<task_id>/cancel', methods=['POST'])
def cancel_task(task_id):
    """Cancel a specific task."""
    task = coordinator.get_task(task_id)
    
    if not task:
        return jsonify({
            'status': 'error',
            'message': f'Task not found: {task_id}'
        }), 404
    
    try:
        result = coordinator.cancel_task(task_id)
        
        return jsonify({
            'status': 'success',
            'task': result
        })
    except Exception as e:
        logger.error(f"Error canceling task: {e}")
        return jsonify({
            'status': 'error',
            'message': str(e)
        }), 400

@app.route('/api/groups', methods=['GET'])
def list_groups():
    """List all agent groups."""
    groups = coordinator.get_agent_groups()
    
    return jsonify({
        'status': 'success',
        'groups': groups,
        'count': len(groups)
    })

@app.route('/api/groups', methods=['POST'])
def create_group():
    """Create a new agent group."""
    data = request.json
    
    group_id = data.get('group_id')
    name = data.get('name', 'New Group')
    description = data.get('description', '')
    agent_ids = data.get('agent_ids', [])
    
    try:
        group = coordinator.create_agent_group(group_id, name, description, agent_ids)
        
        return jsonify({
            'status': 'success',
            'group': group
        }), 201
    except Exception as e:
        logger.error(f"Error creating group: {e}")
        return jsonify({
            'status': 'error',
            'message': str(e)
        }), 400

@app.route('/api/groups/<group_id>', methods=['GET'])
def get_group(group_id):
    """Get information about a specific agent group."""
    group = coordinator.get_agent_group(group_id)
    
    if not group:
        return jsonify({
            'status': 'error',
            'message': f'Group not found: {group_id}'
        }), 404
    
    return jsonify({
        'status': 'success',
        'group': group
    })

@app.route('/api/groups/<group_id>/agents/<agent_id>', methods=['POST'])
def add_agent_to_group(group_id, agent_id):
    """Add an agent to a group."""
    result = coordinator.add_agent_to_group(group_id, agent_id)
    
    if not result:
        return jsonify({
            'status': 'error',
            'message': f'Failed to add agent {agent_id} to group {group_id}'
        }), 400
    
    return jsonify({
        'status': 'success',
        'message': f'Agent {agent_id} added to group {group_id}',
        'group': coordinator.get_agent_group(group_id)
    })

@app.route('/api/groups/<group_id>/agents/<agent_id>', methods=['DELETE'])
def remove_agent_from_group(group_id, agent_id):
    """Remove an agent from a group."""
    result = coordinator.remove_agent_from_group(group_id, agent_id)
    
    if not result:
        return jsonify({
            'status': 'error',
            'message': f'Failed to remove agent {agent_id} from group {group_id}'
        }), 400
    
    return jsonify({
        'status': 'success',
        'message': f'Agent {agent_id} removed from group {group_id}',
        'group': coordinator.get_agent_group(group_id)
    })

@app.route('/api/llm/text', methods=['POST'])
def generate_text():
    """Generate text using the LLM agent."""
    data = request.json
    
    prompt = data.get('prompt')
    provider = data.get('provider')
    options = data.get('options', {})
    
    if not prompt:
        return jsonify({
            'status': 'error',
            'message': 'Prompt is required'
        }), 400
    
    result = llm_agent.execute_task('text_generation', {
        'prompt': prompt,
        'provider': provider,
        'options': options
    })
    
    if result.get('status') == 'error':
        return jsonify(result), 400
    
    return jsonify(result)

@app.route('/api/llm/code', methods=['POST'])
def generate_code():
    """Generate code using the LLM agent."""
    data = request.json
    
    prompt = data.get('prompt')
    language = data.get('language')
    provider = data.get('provider')
    options = data.get('options', {})
    
    if not prompt:
        return jsonify({
            'status': 'error',
            'message': 'Prompt is required'
        }), 400
    
    result = llm_agent.execute_task('code_generation', {
        'prompt': prompt,
        'language': language,
        'provider': provider,
        'options': options
    })
    
    if result.get('status') == 'error':
        return jsonify(result), 400
    
    return jsonify(result)

@app.route('/api/datasets/providers', methods=['GET'])
def list_dataset_providers():
    """List all available dataset providers."""
    result = dataset_agent.execute_task('list_providers', {})
    
    if result.get('status') == 'error':
        return jsonify(result), 400
    
    return jsonify(result)

@app.route('/api/datasets', methods=['GET'])
def list_datasets():
    """List datasets from a provider."""
    provider = request.args.get('provider')
    query = request.args.get('query')
    limit = request.args.get('limit', 10, type=int)
    
    if not provider:
        return jsonify({
            'status': 'error',
            'message': 'Provider is required'
        }), 400
    
    result = dataset_agent.execute_task('list_datasets', {
        'provider': provider,
        'query': query,
        'limit': limit
    })
    
    if result.get('status') == 'error':
        return jsonify(result), 400
    
    return jsonify(result)

@app.route('/api/datasets/<provider>/<path:dataset_id>', methods=['GET'])
def get_dataset(provider, dataset_id):
    """Get a dataset from a provider."""
    result = dataset_agent.execute_task('get_dataset', {
        'provider': provider,
        'dataset_id': dataset_id
    })
    
    if result.get('status') == 'error':
        return jsonify(result), 400
    
    return jsonify(result)

@app.route('/api/datasets/<provider>/<path:dataset_id>/query', methods=['POST'])
def query_dataset(provider, dataset_id):
    """Query a dataset from a provider."""
    data = request.json
    
    query = data.get('query')
    
    if not query:
        return jsonify({
            'status': 'error',
            'message': 'Query is required'
        }), 400
    
    result = dataset_agent.execute_task('query_dataset', {
        'provider': provider,
        'dataset_id': dataset_id,
        'query': query
    })
    
    if result.get('status') == 'error':
        return jsonify(result), 400
    
    return jsonify(result)

@app.route('/api/datasets/<provider>/<path:dataset_id>/analyze', methods=['POST'])
def analyze_dataset(provider, dataset_id):
    """Analyze a dataset from a provider."""
    data = request.json
    
    analysis_type = data.get('analysis_type', 'summary')
    query = data.get('query')
    
    result = dataset_agent.execute_task('data_analysis', {
        'provider': provider,
        'dataset_id': dataset_id,
        'query': query,
        'analysis_type': analysis_type
    })
    
    if result.get('status') == 'error':
        return jsonify(result), 400
    
    return jsonify(result)

@app.route('/api/datasets/<provider>/<path:dataset_id>/visualize', methods=['POST'])
def visualize_dataset(provider, dataset_id):
    """Create a visualization of a dataset from a provider."""
    data = request.json
    
    visualization_type = data.get('visualization_type')
    x_column = data.get('x_column')
    y_column = data.get('y_column')
    group_by = data.get('group_by')
    title = data.get('title')
    output_path = data.get('output_path')
    query = data.get('query')
    
    if not visualization_type:
        return jsonify({
            'status': 'error',
            'message': 'Visualization type is required'
        }), 400
    
    if not x_column:
        return jsonify({
            'status': 'error',
            'message': 'X column is required'
        }), 400
    
    result = dataset_agent.execute_task('data_visualization', {
        'provider': provider,
        'dataset_id': dataset_id,
        'query': query,
        'visualization_type': visualization_type,
        'x_column': x_column,
        'y_column': y_column,
        'group_by': group_by,
        'title': title,
        'output_path': output_path
    })
    
    if result.get('status') == 'error':
        return jsonify(result), 400
    
    return jsonify(result)

@app.route('/api/agents/generate', methods=['POST'])
def generate_agent():
    """Generate a new agent."""
    data = request.json
    
    agent_type = data.get('agent_type')
    capabilities = data.get('capabilities')
    requirements = data.get('requirements')
    provider = data.get('provider')
    options = data.get('options')
    
    if not agent_type:
        return jsonify({
            'status': 'error',
            'message': 'Agent type is required'
        }), 400
    
    if not capabilities:
        return jsonify({
            'status': 'error',
            'message': 'Capabilities are required'
        }), 400
    
    result = generator_agent.execute_task('generate_agent', {
        'agent_type': agent_type,
        'capabilities': capabilities,
        'requirements': requirements,
        'provider': provider,
        'options': options
    })
    
    if result.get('status') == 'error':
        return jsonify(result), 400
    
    return jsonify(result)

@app.route('/api/agents/specialized', methods=['POST'])
def create_specialized_agent():
    """Create a specialized agent based on a task description."""
    data = request.json
    
    task_description = data.get('task_description')
    provider = data.get('provider')
    options = data.get('options')
    
    if not task_description:
        return jsonify({
            'status': 'error',
            'message': 'Task description is required'
        }), 400
    
    result = generator_agent.execute_task('create_specialized_agent', {
        'task_description': task_description,
        'provider': provider,
        'options': options
    })
    
    if result.get('status') == 'error':
        return jsonify(result), 400
    
    return jsonify(result)

@app.route('/api/agents/generated', methods=['GET'])
def list_generated_agents():
    """List all generated agents."""
    agent_type = request.args.get('agent_type')
    
    result = generator_agent.execute_task('list_generated_agents', {
        'agent_type': agent_type
    })
    
    if result.get('status') == 'error':
        return jsonify(result), 400
    
    return jsonify(result)

@app.route('/api/agents/generated/<agent_id>/code', methods=['GET'])
def get_generated_agent_code(agent_id):
    """Get the code for a generated agent."""
    result = generator_agent.execute_task('get_agent_code', {
        'agent_id': agent_id
    })
    
    if result.get('status') == 'error':
        return jsonify(result), 400
    
    return jsonify(result)

def run_api():
    """Run the API server."""
    app.run(host=SERVER_HOST, port=SERVER_PORT, debug=DEBUG)

if __name__ == '__main__':
    run_api()
