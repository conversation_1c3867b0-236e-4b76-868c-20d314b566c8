"""
Logging Utilities

This module provides utilities for logging.
"""

import os
import logging
import logging.handlers
from typing import Optional

def setup_logging(log_file: Optional[str] = None, log_level: str = 'INFO'):
    """
    Set up logging configuration.
    
    Args:
        log_file (str, optional): Path to the log file. If not provided, logs will only be sent to the console.
        log_level (str, optional): Logging level. Default is 'INFO'.
    """
    # Create logs directory if it doesn't exist
    if log_file:
        log_dir = os.path.dirname(log_file)
        if log_dir and not os.path.exists(log_dir):
            os.makedirs(log_dir, exist_ok=True)
    
    # Set up logging level
    numeric_level = getattr(logging, log_level.upper(), None)
    if not isinstance(numeric_level, int):
        raise ValueError(f"Invalid log level: {log_level}")
    
    # Configure root logger
    root_logger = logging.getLogger()
    root_logger.setLevel(numeric_level)
    
    # Remove existing handlers
    for handler in root_logger.handlers[:]:
        root_logger.removeHandler(handler)
    
    # Create console handler
    console_handler = logging.StreamHandler()
    console_handler.setLevel(numeric_level)
    console_format = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
    console_handler.setFormatter(console_format)
    root_logger.addHandler(console_handler)
    
    # Create file handler if log file is provided
    if log_file:
        file_handler = logging.handlers.RotatingFileHandler(
            log_file, maxBytes=10*1024*1024, backupCount=5
        )
        file_handler.setLevel(numeric_level)
        file_format = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
        file_handler.setFormatter(file_format)
        root_logger.addHandler(file_handler)
    
    # Set up specific loggers
    setup_module_loggers(numeric_level)
    
    logging.info(f"Logging initialized with level: {log_level}")

def setup_module_loggers(log_level: int):
    """
    Set up loggers for specific modules.
    
    Args:
        log_level (int): Logging level.
    """
    # Set up Flask logger
    flask_logger = logging.getLogger('werkzeug')
    flask_logger.setLevel(log_level)
    
    # Set up requests logger (more verbose, so set to WARNING by default)
    requests_logger = logging.getLogger('requests')
    requests_logger.setLevel(logging.WARNING)
    
    # Set up urllib3 logger (more verbose, so set to WARNING by default)
    urllib3_logger = logging.getLogger('urllib3')
    urllib3_logger.setLevel(logging.WARNING)
    
    # Set up AI Agent System loggers
    ai_agent_system_logger = logging.getLogger('ai_agent_system')
    ai_agent_system_logger.setLevel(log_level)
    
    # Set up specific module loggers
    core_logger = logging.getLogger('ai_agent_system.core')
    core_logger.setLevel(log_level)
    
    llm_logger = logging.getLogger('ai_agent_system.llm')
    llm_logger.setLevel(log_level)
    
    datasets_logger = logging.getLogger('ai_agent_system.datasets')
    datasets_logger.setLevel(log_level)
    
    agents_logger = logging.getLogger('ai_agent_system.agents')
    agents_logger.setLevel(log_level)
    
    api_logger = logging.getLogger('ai_agent_system.api')
    api_logger.setLevel(log_level)
