from flask import Blueprint, jsonify, request
from flask_jwt_extended import create_access_token, jwt_required, get_jwt_identity
from werkzeug.security import generate_password_hash, check_password_hash
from datetime import timedelta

auth_bp = Blueprint('auth', __name__)

# In-memory user storage (replace with database in production)
users = {
    '<EMAIL>': {
        'email': '<EMAIL>',
        'password': generate_password_hash('admin123'),
        'name': 'Admin User'
    }
}

@auth_bp.route('/auth/register', methods=['POST'])
def register():
    try:
        data = request.get_json()
        if not data or not data.get('email') or not data.get('password'):
            return jsonify({'error': 'Missing email or password'}), 400
        
        if data['email'] in users:
            return jsonify({'error': 'User already exists'}), 400
        
        users[data['email']] = {
            'email': data['email'],
            'password': generate_password_hash(data['password']),
            'name': data.get('name', '')
        }
        
        return jsonify({'message': 'User registered successfully'}), 201
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@auth_bp.route('/auth/login', methods=['POST'])
def login():
    try:
        data = request.get_json()
        if not data or not data.get('email') or not data.get('password'):
            return jsonify({'error': 'Missing email or password'}), 400
        
        user = users.get(data['email'])
        if not user or not check_password_hash(user['password'], data['password']):
            return jsonify({'error': 'Invalid credentials'}), 401
        
        access_token = create_access_token(
            identity=data['email'],
            expires_delta=timedelta(hours=1)
        )
        return jsonify({'access_token': access_token}), 200
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@auth_bp.route('/auth/me', methods=['GET'])
@jwt_required()
def get_current_user():
    try:
        current_user = get_jwt_identity()
        user = users.get(current_user)
        if not user:
            return jsonify({'error': 'User not found'}), 404
        
        return jsonify({
            'email': user['email'],
            'name': user['name']
        }), 200
    except Exception as e:
        return jsonify({'error': str(e)}), 500 