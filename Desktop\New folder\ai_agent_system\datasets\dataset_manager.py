"""
Dataset Manager Module

This module provides a manager for dataset providers.
"""

import logging
from typing import Dict, List, Any, Optional, Union

from ai_agent_system.datasets.dataset_provider import DatasetProvider, KaggleProvider, BigQueryProvider, S3Provider

# Configure logging
logger = logging.getLogger(__name__)

class DatasetManager:
    """
    Manager for dataset providers.
    """
    
    def __init__(self):
        """Initialize the dataset manager."""
        # Dictionary to store dataset providers
        self.providers = {}
        
        # Register built-in providers
        self._register_built_in_providers()
        
        logger.info("Dataset Manager initialized")
    
    def _register_built_in_providers(self) -> None:
        """Register built-in dataset providers."""
        try:
            # Register Kaggle provider
            self.register_provider('kaggle', KaggleProvider)
            
            # Register BigQuery provider
            self.register_provider('bigquery', BigQueryProvider)
            
            # Register S3 provider
            self.register_provider('s3', S3Provider)
            
        except Exception as e:
            logger.error(f"Error registering built-in providers: {e}")
    
    def register_provider(self, provider_name: str, provider_class: Any) -> None:
        """
        Register a dataset provider.
        
        Args:
            provider_name (str): Name of the provider
            provider_class (type): Provider class
        """
        try:
            # Create an instance of the provider
            provider = provider_class()
            
            # Register the provider
            self.providers[provider_name] = provider
            
            logger.info(f"Registered dataset provider: {provider_name}")
            
        except Exception as e:
            logger.error(f"Error registering provider {provider_name}: {e}")
    
    def get_provider(self, provider_name: str) -> DatasetProvider:
        """
        Get a dataset provider.
        
        Args:
            provider_name (str): Name of the provider
            
        Returns:
            DatasetProvider: The dataset provider
        """
        if provider_name not in self.providers:
            raise ValueError(f"Unknown dataset provider: {provider_name}")
        
        return self.providers[provider_name]
    
    def get_available_providers(self) -> List[str]:
        """
        Get a list of available dataset providers.
        
        Returns:
            list: List of available dataset providers
        """
        return list(self.providers.keys())
    
    def list_datasets(self, provider_name: str, query: Optional[str] = None, limit: int = 10) -> Dict[str, Any]:
        """
        List available datasets from a provider.
        
        Args:
            provider_name (str): Name of the provider
            query (str, optional): Query to search for datasets
            limit (int, optional): Maximum number of datasets to return
            
        Returns:
            dict: List of datasets and metadata
        """
        provider = self.get_provider(provider_name)
        return provider.list_datasets(query, limit)
    
    def get_dataset(self, provider_name: str, dataset_id: str) -> Dict[str, Any]:
        """
        Get a dataset from a provider.
        
        Args:
            provider_name (str): Name of the provider
            dataset_id (str): ID of the dataset
            
        Returns:
            dict: The dataset and metadata
        """
        provider = self.get_provider(provider_name)
        return provider.get_dataset(dataset_id)
    
    def query_dataset(self, provider_name: str, dataset_id: str, query: str) -> Dict[str, Any]:
        """
        Query a dataset from a provider.
        
        Args:
            provider_name (str): Name of the provider
            dataset_id (str): ID of the dataset
            query (str): Query to run on the dataset
            
        Returns:
            dict: Query results and metadata
        """
        provider = self.get_provider(provider_name)
        return provider.query_dataset(dataset_id, query)
