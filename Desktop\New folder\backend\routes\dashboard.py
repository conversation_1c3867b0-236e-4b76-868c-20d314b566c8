from flask import Blueprint, jsonify
from flask_jwt_extended import jwt_required, get_jwt_identity
import random
from datetime import datetime, timedelta

dashboard_bp = Blueprint('dashboard', __name__)

@dashboard_bp.route('/api/dashboard', methods=['GET'])
@jwt_required()
def get_dashboard():
    try:
        # Get the current user's identity
        current_user = get_jwt_identity()
        
        # Simulate system status
        system_status = {
            'api_status': 'Operational',
            'db_connection': 'Connected',
            'storage_status': 'Normal'
        }
        
        # Simulate project metrics
        project_metrics = {
            'total_projects': random.randint(5, 20),
            'active_projects': random.randint(1, 5),
            'storage_used': random.randint(100, 1000),
            'storage_limit': 1000
        }
        
        # Simulate performance metrics
        performance_metrics = {
            'avg_response_time': random.randint(100, 500),
            'success_rate': random.randint(95, 100),
            'error_rate': random.randint(0, 5)
        }
        
        return jsonify({
            'system_status': system_status,
            'project_metrics': project_metrics,
            'performance_metrics': performance_metrics
        }), 200
        
    except Exception as e:
        return jsonify({'message': str(e)}), 500

@dashboard_bp.route('/api/projects', methods=['GET'])
@jwt_required()
def get_projects():
    try:
        current_user = get_jwt_identity()
        
        # Simulate project data
        projects = []
        for i in range(5):
            projects.append({
                'id': i + 1,
                'name': f'Project {i + 1}',
                'description': f'Description for Project {i + 1}',
                'status': random.choice(['Active', 'Inactive', 'Maintenance']),
                'type': random.choice(['Web', 'Mobile', 'API']),
                'storage_used': random.randint(10, 100),
                'storage_limit': 100,
                'test_count': random.randint(5, 20)
            })
        
        return jsonify(projects), 200
        
    except Exception as e:
        return jsonify({'message': str(e)}), 500

@dashboard_bp.route('/api/agents/metrics', methods=['GET'])
@jwt_required()
def get_agent_metrics():
    try:
        current_user = get_jwt_identity()
        
        # Simulate agent metrics
        agent_metrics = {
            'total_agents': random.randint(10, 50),
            'active_agents': random.randint(5, 20),
            'agent_types': {
                'web': random.randint(2, 10),
                'mobile': random.randint(2, 10),
                'api': random.randint(2, 10)
            },
            'performance': {
                'avg_response_time': random.randint(50, 200),
                'success_rate': random.randint(95, 100)
            },
            'security': {
                'threats_detected': random.randint(0, 5),
                'last_scan': (datetime.now() - timedelta(hours=random.randint(1, 24))).isoformat()
            },
            'test_metrics': {
                'coverage': random.randint(70, 100),
                'pass_rate': random.randint(90, 100)
            }
        }
        
        return jsonify(agent_metrics), 200
        
    except Exception as e:
        return jsonify({'message': str(e)}), 500 