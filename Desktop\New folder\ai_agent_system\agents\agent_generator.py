"""
Agent Generator Module

This module provides an agent that can generate other agents.
"""

import os
import importlib.util
import logging
from typing import Dict, List, Any, Optional

from ai_agent_system.core.base_agent import BaseAgent
from ai_agent_system.llm.llm_manager import LLMManager
from ai_agent_system.core.agent_factory import AgentFactory

# Configure logging
logger = logging.getLogger(__name__)

class AgentGenerator(BaseAgent):
    """
    Agent that can generate other agents.
    """

    def __init__(self, agent_id: Optional[str] = None, capabilities: Optional[List[str]] = None, **kwargs):
        """
        Initialize the agent generator.

        Args:
            agent_id (str, optional): Unique identifier for the agent
            capabilities (list, optional): List of capabilities for the agent
            **kwargs: Additional arguments to pass to the parent constructor
        """
        # Set default capabilities if not provided
        if capabilities is None:
            capabilities = [
                'generate_agent',
                'create_specialized_agent',
                'list_generated_agents',
                'get_agent_code',
                'register_agent'
            ]

        # Initialize the parent class
        super().__init__(agent_id=agent_id, capabilities=capabilities, **kwargs)

        # Initialize LLM manager
        self.llm_manager = LLMManager()

        # Initialize agent factory
        self.agent_factory = AgentFactory()

        # Dictionary to store generated agents
        self.generated_agents = {}

        # Create directories for generated agents
        self.generated_agents_dir = os.environ.get('GENERATED_AGENTS_DIR', 'generated_agents')
        os.makedirs(self.generated_agents_dir, exist_ok=True)

        logger.info(f"Agent Generator initialized")

    def generate_agent(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """
        Generate an agent.

        Args:
            params (dict): Parameters for agent generation
                - agent_type (str): The type of agent to generate
                - capabilities (list): List of capabilities the agent should have
                - requirements (dict, optional): Specific requirements for the agent
                - provider (str, optional): The LLM provider to use
                - options (dict, optional): Additional options for the LLM

        Returns:
            dict: Agent generation result
        """
        # Extract parameters
        agent_type = params.get('agent_type')
        capabilities = params.get('capabilities', [])
        requirements = params.get('requirements')
        provider = params.get('provider')
        options = params.get('options', {})

        if not agent_type:
            return {
                'status': 'error',
                'message': "Agent type is required for agent generation"
            }

        if not capabilities:
            return {
                'status': 'error',
                'message': "At least one capability is required for agent generation"
            }

        try:
            # Generate agent code using the LLM manager
            result = self.llm_manager.generate_agent_code(agent_type, capabilities, requirements, provider, options)

            if result['status'] != 'success':
                return result

            # Save the agent code to a file
            agent_id = f"{agent_type}_agent_{len(self.generated_agents) + 1}"
            file_path = os.path.join(self.generated_agents_dir, f"{agent_id}.py")

            with open(file_path, 'w') as f:
                f.write(result['agent_code'])

            # Store the agent information
            self.generated_agents[agent_id] = {
                'agent_id': agent_id,
                'agent_type': agent_type,
                'capabilities': capabilities,
                'requirements': requirements,
                'file_path': file_path,
                'model': result['model'],
                'usage': result.get('usage')
            }

            # Register the agent class
            self.register_agent({'agent_id': agent_id})

            return {
                'status': 'success',
                'agent_id': agent_id,
                'agent_type': agent_type,
                'capabilities': capabilities,
                'file_path': file_path
            }

        except Exception as e:
            logger.error(f"Error generating agent: {e}")
            return {
                'status': 'error',
                'message': f"Error generating agent: {str(e)}"
            }

    def create_specialized_agent(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """
        Create a specialized agent based on a task description.

        Args:
            params (dict): Parameters for specialized agent creation
                - task_description (str): Description of the task the agent should perform
                - provider (str, optional): The LLM provider to use
                - options (dict, optional): Additional options for the LLM

        Returns:
            dict: Specialized agent creation result
        """
        # Extract parameters
        task_description = params.get('task_description')
        provider = params.get('provider')
        options = params.get('options', {})

        if not task_description:
            return {
                'status': 'error',
                'message': "Task description is required for specialized agent creation"
            }

        try:
            # Generate a prompt to determine agent type and capabilities
            prompt = f"""
Based on the following task description, determine the most appropriate agent type and capabilities:

Task Description: {task_description}

Respond with a JSON object containing:
1. agent_type: A short, descriptive name for the agent type (e.g., 'web_scraper', 'data_analyzer', 'content_generator')
2. capabilities: A list of specific capabilities the agent should have to perform the task
3. requirements: Any specific requirements or dependencies the agent might need
"""

            # Generate agent specifications using the LLM manager
            result = self.llm_manager.generate_text(prompt, provider, options)

            if result['status'] != 'success':
                return result

            # Parse the agent specifications
            import json
            import re

            # Extract JSON from the response
            json_match = re.search(r'```json\n(.*?)\n```', result['text'], re.DOTALL)
            if json_match:
                agent_specs = json.loads(json_match.group(1))
            else:
                # Try to find JSON without code blocks
                json_match = re.search(r'({.*})', result['text'], re.DOTALL)
                if json_match:
                    agent_specs = json.loads(json_match.group(1))
                else:
                    return {
                        'status': 'error',
                        'message': "Could not parse agent specifications from LLM response"
                    }

            # Generate the agent
            agent_result = self.generate_agent({
                'agent_type': agent_specs['agent_type'],
                'capabilities': agent_specs['capabilities'],
                'requirements': agent_specs.get('requirements'),
                'provider': provider,
                'options': options
            })

            if agent_result['status'] != 'success':
                return agent_result

            # Add task description to the agent information
            self.generated_agents[agent_result['agent_id']]['task_description'] = task_description

            return {
                'status': 'success',
                'agent_id': agent_result['agent_id'],
                'agent_type': agent_specs['agent_type'],
                'capabilities': agent_specs['capabilities'],
                'task_description': task_description,
                'file_path': agent_result['file_path']
            }

        except Exception as e:
            logger.error(f"Error creating specialized agent: {e}")
            return {
                'status': 'error',
                'message': f"Error creating specialized agent: {str(e)}"
            }

    def list_generated_agents(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """
        List all generated agents.

        Args:
            params (dict): Parameters for listing agents
                - agent_type (str, optional): Filter by agent type

        Returns:
            dict: List of generated agents
        """
        # Extract parameters
        agent_type = params.get('agent_type')

        try:
            # Filter agents by type if specified
            if agent_type:
                agents = {k: v for k, v in self.generated_agents.items() if v['agent_type'] == agent_type}
            else:
                agents = self.generated_agents

            return {
                'status': 'success',
                'agents': agents,
                'count': len(agents)
            }

        except Exception as e:
            logger.error(f"Error listing generated agents: {e}")
            return {
                'status': 'error',
                'message': f"Error listing generated agents: {str(e)}"
            }

    def get_agent_code(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """
        Get the code for a generated agent.

        Args:
            params (dict): Parameters for getting agent code
                - agent_id (str): ID of the agent

        Returns:
            dict: Agent code
        """
        # Extract parameters
        agent_id = params.get('agent_id')

        if not agent_id:
            return {
                'status': 'error',
                'message': "Agent ID is required for getting agent code"
            }

        try:
            # Check if the agent exists
            if agent_id not in self.generated_agents:
                return {
                    'status': 'error',
                    'message': f"Agent not found: {agent_id}"
                }

            # Get the agent file path
            file_path = self.generated_agents[agent_id]['file_path']

            # Read the agent code
            with open(file_path, 'r') as f:
                code = f.read()

            return {
                'status': 'success',
                'agent_id': agent_id,
                'agent_type': self.generated_agents[agent_id]['agent_type'],
                'code': code,
                'file_path': file_path
            }

        except Exception as e:
            logger.error(f"Error getting agent code: {e}")
            return {
                'status': 'error',
                'message': f"Error getting agent code: {str(e)}"
            }

    def register_agent(self, params: Dict[str, Any]) -> Dict[str, Any]:
        """
        Register a generated agent with the agent factory.

        Args:
            params (dict): Parameters for registering an agent
                - agent_id (str): ID of the agent

        Returns:
            dict: Registration result
        """
        # Extract parameters
        agent_id = params.get('agent_id')

        if not agent_id:
            return {
                'status': 'error',
                'message': "Agent ID is required for registering an agent"
            }

        try:
            # Check if the agent exists
            if agent_id not in self.generated_agents:
                return {
                    'status': 'error',
                    'message': f"Agent not found: {agent_id}"
                }

            # Get the agent information
            agent_info = self.generated_agents[agent_id]

            # Register the agent class with the agent factory
            self.agent_factory.register_agent_class_from_file(agent_info['agent_type'], agent_info['file_path'])

            return {
                'status': 'success',
                'agent_id': agent_id,
                'agent_type': agent_info['agent_type'],
                'message': f"Agent registered: {agent_id}"
            }

        except Exception as e:
            logger.error(f"Error registering agent: {e}")
            return {
                'status': 'error',
                'message': f"Error registering agent: {str(e)}"
            }
