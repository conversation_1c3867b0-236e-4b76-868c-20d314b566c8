"""
Base Agent Template

This template can be used to create new agents with the AI Agent System.
"""

from ai_agent_system.core.base_agent import BaseAgent

class {agent_type}Agent(BaseAgent):
    """
    {agent_type_capitalized} agent for the AI agent system.
    """
    
    def __init__(self, agent_id=None, capabilities=None, **kwargs):
        """
        Initialize the {agent_type} agent.
        
        Args:
            agent_id (str, optional): Unique identifier for the agent
            capabilities (list, optional): List of capabilities for the agent
            **kwargs: Additional arguments to pass to the parent constructor
        """
        # Set default capabilities if not provided
        if capabilities is None:
            capabilities = [{capabilities_list}]
        
        # Initialize the parent class
        super().__init__(agent_id=agent_id, agent_type='{agent_type}', capabilities=capabilities, **kwargs)
    
    # Implement capability methods here
    {capability_methods}
