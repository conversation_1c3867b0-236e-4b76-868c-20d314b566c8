from flask import Blueprint, request, jsonify
from functools import wraps
import stripe
from datetime import datetime
from models.subscription import Subscription, Plan
from models.user import User
from database import db
import os

subscription_bp = Blueprint('subscription', __name__)

# Initialize Stripe
stripe.api_key = os.getenv('STRIPE_SECRET_KEY')

# Subscription Plans
PLANS = {
    'basic': {
        'name': 'Basic',
        'price_id': 'price_basic_monthly',
        'features': {
            'projects_limit': 3,
            'agents_per_project': 2,
            'api_calls_per_month': 1000,
            'support_level': 'email',
            'custom_agents': False,
            'advanced_analytics': False
        },
        'price': 49.99
    },
    'professional': {
        'name': 'Professional',
        'price_id': 'price_pro_monthly',
        'features': {
            'projects_limit': 10,
            'agents_per_project': 5,
            'api_calls_per_month': 5000,
            'support_level': 'priority',
            'custom_agents': True,
            'advanced_analytics': True
        },
        'price': 99.99
    },
    'enterprise': {
        'name': 'Enterprise',
        'price_id': 'price_enterprise_monthly',
        'features': {
            'projects_limit': -1,  # Unlimited
            'agents_per_project': -1,  # Unlimited
            'api_calls_per_month': -1,  # Unlimited
            'support_level': 'dedicated',
            'custom_agents': True,
            'advanced_analytics': True
        },
        'price': 499.99
    }
}

def require_subscription(minimum_plan='basic'):
    """Decorator to check if user has required subscription level."""
    def decorator(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            user = get_current_user()  # Implement this based on your auth system
            if not user.subscription:
                return jsonify({
                    'error': 'Subscription required',
                    'message': 'Please subscribe to access this feature'
                }), 403
            
            plan_levels = ['basic', 'professional', 'enterprise']
            required_level = plan_levels.index(minimum_plan)
            current_level = plan_levels.index(user.subscription.plan)
            
            if current_level < required_level:
                return jsonify({
                    'error': 'Insufficient subscription level',
                    'message': f'This feature requires {minimum_plan} plan or higher'
                }), 403
            
            return f(*args, **kwargs)
        return decorated_function
    return decorator

@subscription_bp.route('/plans', methods=['GET'])
def get_plans():
    """Get available subscription plans."""
    return jsonify({
        'status': 'success',
        'plans': PLANS
    })

@subscription_bp.route('/subscribe', methods=['POST'])
def subscribe():
    """Create a new subscription."""
    data = request.json
    user = get_current_user()
    plan_id = data.get('plan_id')
    payment_method_id = data.get('payment_method_id')

    if not plan_id or not payment_method_id:
        return jsonify({
            'error': 'Missing required fields',
            'message': 'Plan ID and payment method ID are required'
        }), 400

    try:
        # Create or get Stripe customer
        if not user.stripe_customer_id:
            customer = stripe.Customer.create(
                email=user.email,
                payment_method=payment_method_id,
                invoice_settings={
                    'default_payment_method': payment_method_id
                }
            )
            user.stripe_customer_id = customer.id
            db.session.commit()
        else:
            # Update default payment method
            stripe.Customer.modify(
                user.stripe_customer_id,
                invoice_settings={
                    'default_payment_method': payment_method_id
                }
            )

        # Create subscription
        subscription = stripe.Subscription.create(
            customer=user.stripe_customer_id,
            items=[{'price': PLANS[plan_id]['price_id']}],
            expand=['latest_invoice.payment_intent']
        )

        # Save subscription details
        db_subscription = Subscription(
            user_id=user.id,
            stripe_subscription_id=subscription.id,
            plan=plan_id,
            status=subscription.status,
            current_period_end=datetime.fromtimestamp(subscription.current_period_end)
        )
        db.session.add(db_subscription)
        db.session.commit()

        return jsonify({
            'status': 'success',
            'subscription': {
                'id': subscription.id,
                'status': subscription.status,
                'client_secret': subscription.latest_invoice.payment_intent.client_secret
            }
        })

    except stripe.error.StripeError as e:
        return jsonify({
            'error': 'Payment failed',
            'message': str(e)
        }), 400

@subscription_bp.route('/cancel', methods=['POST'])
def cancel_subscription():
    """Cancel subscription."""
    user = get_current_user()
    
    if not user.subscription:
        return jsonify({
            'error': 'No active subscription',
            'message': 'No subscription found to cancel'
        }), 404

    try:
        # Cancel at period end
        stripe.Subscription.modify(
            user.subscription.stripe_subscription_id,
            cancel_at_period_end=True
        )

        user.subscription.status = 'canceling'
        db.session.commit()

        return jsonify({
            'status': 'success',
            'message': 'Subscription will be canceled at the end of the billing period'
        })

    except stripe.error.StripeError as e:
        return jsonify({
            'error': 'Cancellation failed',
            'message': str(e)
        }), 400

@subscription_bp.route('/usage', methods=['GET'])
def get_usage():
    """Get current subscription usage."""
    user = get_current_user()
    
    if not user.subscription:
        return jsonify({
            'error': 'No active subscription',
            'message': 'No subscription found'
        }), 404

    # Get usage from your tracking system
    usage = get_user_usage(user.id)  # Implement this based on your usage tracking
    plan = PLANS[user.subscription.plan]

    return jsonify({
        'status': 'success',
        'usage': {
            'projects': {
                'used': usage['projects_count'],
                'limit': plan['features']['projects_limit'],
                'percentage': calculate_usage_percentage(
                    usage['projects_count'],
                    plan['features']['projects_limit']
                )
            },
            'api_calls': {
                'used': usage['api_calls_count'],
                'limit': plan['features']['api_calls_per_month'],
                'percentage': calculate_usage_percentage(
                    usage['api_calls_count'],
                    plan['features']['api_calls_per_month']
                )
            },
            'agents': {
                'used': usage['agents_count'],
                'limit': plan['features']['agents_per_project'],
                'percentage': calculate_usage_percentage(
                    usage['agents_count'],
                    plan['features']['agents_per_project']
                )
            }
        },
        'billing_period': {
            'start': user.subscription.current_period_start.isoformat(),
            'end': user.subscription.current_period_end.isoformat()
        }
    })

@subscription_bp.route('/invoices', methods=['GET'])
def get_invoices():
    """Get user's invoices."""
    user = get_current_user()
    
    if not user.stripe_customer_id:
        return jsonify({
            'error': 'No billing history',
            'message': 'No billing history found'
        }), 404

    try:
        invoices = stripe.Invoice.list(
            customer=user.stripe_customer_id,
            limit=12  # Last 12 invoices
        )

        return jsonify({
            'status': 'success',
            'invoices': [{
                'id': invoice.id,
                'amount_paid': invoice.amount_paid / 100,  # Convert from cents
                'currency': invoice.currency,
                'status': invoice.status,
                'created': datetime.fromtimestamp(invoice.created).isoformat(),
                'invoice_pdf': invoice.invoice_pdf
            } for invoice in invoices.data]
        })

    except stripe.error.StripeError as e:
        return jsonify({
            'error': 'Failed to fetch invoices',
            'message': str(e)
        }), 400

@subscription_bp.route('/webhook', methods=['POST'])
def handle_webhook():
    """Handle Stripe webhooks."""
    payload = request.data
    sig_header = request.headers.get('Stripe-Signature')

    try:
        event = stripe.Webhook.construct_event(
            payload, sig_header, os.getenv('STRIPE_WEBHOOK_SECRET')
        )
    except ValueError as e:
        return jsonify({'error': 'Invalid payload'}), 400
    except stripe.error.SignatureVerificationError as e:
        return jsonify({'error': 'Invalid signature'}), 400

    if event.type == 'customer.subscription.updated':
        handle_subscription_updated(event.data.object)
    elif event.type == 'customer.subscription.deleted':
        handle_subscription_deleted(event.data.object)
    elif event.type == 'invoice.payment_succeeded':
        handle_payment_succeeded(event.data.object)
    elif event.type == 'invoice.payment_failed':
        handle_payment_failed(event.data.object)

    return jsonify({'status': 'success'}) 