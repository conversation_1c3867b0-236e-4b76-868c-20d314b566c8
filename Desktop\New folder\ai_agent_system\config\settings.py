"""
Settings module for the AI Agent System.

This module loads environment variables and provides configuration settings.
"""

import os
import logging
from typing import Dict, Any, Optional
# Try different import methods for dotenv
try:
    from dotenv import load_dotenv
except ImportError:
    try:
        from python_dotenv import load_dotenv
    except ImportError:
        print("Warning: dotenv module not found. Environment variables will not be loaded from .env file.")
        def load_dotenv():
            pass

# Load environment variables from .env file
load_dotenv()

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Server settings
SERVER_HOST = os.environ.get('SERVER_HOST', '0.0.0.0')
SERVER_PORT = int(os.environ.get('SERVER_PORT', 8000))
DEBUG = os.environ.get('DEBUG', 'True').lower() in ('true', '1', 't')

# Database settings
DATABASE_URL = os.environ.get('DATABASE_URL', 'sqlite:///ai_agent_system.db')

# Redis settings
REDIS_URL = os.environ.get('REDIS_URL', 'redis://localhost:6379/0')

# Security settings
SECRET_KEY = os.environ.get('SECRET_KEY', 'development-secret-key-change-in-production')
JWT_EXPIRATION = int(os.environ.get('JWT_EXPIRATION', 3600))  # 1 hour

# LLM settings
OPENAI_API_KEY = os.environ.get('OPENAI_API_KEY')
OPENAI_MODEL = os.environ.get('OPENAI_MODEL', 'gpt-4')
ANTHROPIC_API_KEY = os.environ.get('ANTHROPIC_API_KEY')
ANTHROPIC_MODEL = os.environ.get('ANTHROPIC_MODEL', 'claude-3-opus-20240229')
HF_API_KEY = os.environ.get('HF_API_KEY')
HF_MODEL = os.environ.get('HF_MODEL', 'mistralai/Mistral-7B-Instruct-v0.1')

# Dataset settings
KAGGLE_USERNAME = os.environ.get('KAGGLE_USERNAME')
KAGGLE_KEY = os.environ.get('KAGGLE_KEY')
GOOGLE_APPLICATION_CREDENTIALS = os.environ.get('GOOGLE_APPLICATION_CREDENTIALS')
AWS_ACCESS_KEY_ID = os.environ.get('AWS_ACCESS_KEY_ID')
AWS_SECRET_ACCESS_KEY = os.environ.get('AWS_SECRET_ACCESS_KEY')
AWS_REGION = os.environ.get('AWS_REGION', 'us-east-1')

# Monitoring settings
PROMETHEUS_PORT = int(os.environ.get('PROMETHEUS_PORT', 9090))
GRAFANA_PORT = int(os.environ.get('GRAFANA_PORT', 3000))

# Logging settings
LOG_LEVEL = os.environ.get('LOG_LEVEL', 'INFO')
LOG_FILE = os.environ.get('LOG_FILE', 'ai_agent_system.log')

# Agent settings
AGENT_TEMPLATES_DIR = os.environ.get('AGENT_TEMPLATES_DIR', 'agent_templates')
GENERATED_AGENTS_DIR = os.environ.get('GENERATED_AGENTS_DIR', 'generated_agents')

# Check required settings
def check_required_settings():
    """Check if required settings are set."""
    missing_settings = []

    # Check LLM settings
    if not OPENAI_API_KEY and not ANTHROPIC_API_KEY and not HF_API_KEY:
        missing_settings.append("At least one of OPENAI_API_KEY, ANTHROPIC_API_KEY, or HF_API_KEY")

    if missing_settings:
        logger.warning(f"Missing required settings: {', '.join(missing_settings)}")
        return False

    return True

# Get LLM provider settings
def get_llm_provider_settings(provider: str) -> Dict[str, Any]:
    """
    Get settings for a specific LLM provider.

    Args:
        provider (str): The LLM provider name (openai, anthropic, huggingface)

    Returns:
        dict: The provider settings
    """
    if provider == 'openai':
        return {
            'api_key': OPENAI_API_KEY,
            'model': OPENAI_MODEL
        }
    elif provider == 'anthropic':
        return {
            'api_key': ANTHROPIC_API_KEY,
            'model': ANTHROPIC_MODEL
        }
    elif provider == 'huggingface':
        return {
            'api_key': HF_API_KEY,
            'model': HF_MODEL
        }
    else:
        raise ValueError(f"Unsupported LLM provider: {provider}")

# Get dataset provider settings
def get_dataset_provider_settings(provider: str) -> Dict[str, Any]:
    """
    Get settings for a specific dataset provider.

    Args:
        provider (str): The dataset provider name (kaggle, bigquery, s3)

    Returns:
        dict: The provider settings
    """
    if provider == 'kaggle':
        return {
            'username': KAGGLE_USERNAME,
            'key': KAGGLE_KEY
        }
    elif provider == 'bigquery':
        return {
            'credentials_path': GOOGLE_APPLICATION_CREDENTIALS
        }
    elif provider == 's3':
        return {
            'access_key_id': AWS_ACCESS_KEY_ID,
            'secret_access_key': AWS_SECRET_ACCESS_KEY,
            'region': AWS_REGION
        }
    else:
        raise ValueError(f"Unsupported dataset provider: {provider}")

# Get default LLM provider
def get_default_llm_provider() -> Optional[str]:
    """
    Get the default LLM provider based on available API keys.

    Returns:
        str: The default LLM provider name or None if no provider is available
    """
    if OPENAI_API_KEY:
        return 'openai'
    elif ANTHROPIC_API_KEY:
        return 'anthropic'
    elif HF_API_KEY:
        return 'huggingface'
    else:
        return None
