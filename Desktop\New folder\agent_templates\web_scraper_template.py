"""
Web Scraper Agent Template

This template can be used to create web scraper agents.
"""

from ai_agent_system.core.base_agent import BaseAgent

class WebScraperAgent(BaseAgent):
    """
    Web scraper agent for the AI agent system.
    """
    
    def __init__(self, agent_id=None, capabilities=None, **kwargs):
        """
        Initialize the web scraper agent.
        
        Args:
            agent_id (str, optional): Unique identifier for the agent
            capabilities (list, optional): List of capabilities for the agent
            **kwargs: Additional arguments to pass to the parent constructor
        """
        # Set default capabilities if not provided
        if capabilities is None:
            capabilities = [
                'scrape_url',
                'scrape_products',
                'extract_data',
                'save_to_csv'
            ]
        
        # Initialize the parent class
        super().__init__(agent_id=agent_id, agent_type='web_scraper', capabilities=capabilities, **kwargs)
    
    def scrape_url(self, params):
        """
        Scrape a URL.
        
        Args:
            params (dict): Parameters for scraping
                - url (str): The URL to scrape
                - headers (dict, optional): HTTP headers to use
                - timeout (int, optional): Timeout in seconds
                
        Returns:
            dict: Scraping result
        """
        # Extract parameters
        url = params.get('url')
        headers = params.get('headers', {})
        timeout = params.get('timeout', 30)
        
        if not url:
            return {
                'status': 'error',
                'message': "URL is required for scraping"
            }
        
        try:
            # Import requests here to avoid dependency issues
            import requests
            
            # Send the request
            response = requests.get(url, headers=headers, timeout=timeout)
            response.raise_for_status()
            
            # Return the response
            return {
                'status': 'success',
                'url': url,
                'status_code': response.status_code,
                'content_type': response.headers.get('Content-Type'),
                'content': response.text
            }
            
        except ImportError:
            return {
                'status': 'error',
                'message': "Requests library not available. Please install it with 'pip install requests'"
            }
        except Exception as e:
            return {
                'status': 'error',
                'message': f"Error scraping URL: {str(e)}"
            }
    
    def scrape_products(self, params):
        """
        Scrape product information from an e-commerce website.
        
        Args:
            params (dict): Parameters for scraping
                - url (str): The URL of the e-commerce website
                - selectors (dict, optional): CSS selectors for product elements
                
        Returns:
            dict: Scraping result
        """
        # Extract parameters
        url = params.get('url')
        selectors = params.get('selectors', {})
        
        if not url:
            return {
                'status': 'error',
                'message': "URL is required for scraping products"
            }
        
        try:
            # Import required libraries
            import requests
            from bs4 import BeautifulSoup
            
            # Set default selectors if not provided
            if not selectors:
                selectors = {
                    'product': '.product',
                    'name': '.product-name',
                    'price': '.product-price',
                    'image': '.product-image',
                    'description': '.product-description'
                }
            
            # Scrape the URL
            response = requests.get(url)
            response.raise_for_status()
            
            # Parse the HTML
            soup = BeautifulSoup(response.text, 'html.parser')
            
            # Extract product information
            products = []
            for product_element in soup.select(selectors['product']):
                product = {}
                
                # Extract product name
                name_element = product_element.select_one(selectors['name'])
                if name_element:
                    product['name'] = name_element.text.strip()
                
                # Extract product price
                price_element = product_element.select_one(selectors['price'])
                if price_element:
                    product['price'] = price_element.text.strip()
                
                # Extract product image
                image_element = product_element.select_one(selectors['image'])
                if image_element and image_element.has_attr('src'):
                    product['image'] = image_element['src']
                
                # Extract product description
                description_element = product_element.select_one(selectors['description'])
                if description_element:
                    product['description'] = description_element.text.strip()
                
                products.append(product)
            
            return {
                'status': 'success',
                'url': url,
                'products': products,
                'count': len(products)
            }
            
        except ImportError:
            return {
                'status': 'error',
                'message': "Required libraries not available. Please install them with 'pip install requests beautifulsoup4'"
            }
        except Exception as e:
            return {
                'status': 'error',
                'message': f"Error scraping products: {str(e)}"
            }
    
    def extract_data(self, params):
        """
        Extract data from HTML content.
        
        Args:
            params (dict): Parameters for extraction
                - content (str): The HTML content to extract data from
                - selectors (dict): CSS selectors for elements to extract
                
        Returns:
            dict: Extraction result
        """
        # Extract parameters
        content = params.get('content')
        selectors = params.get('selectors')
        
        if not content:
            return {
                'status': 'error',
                'message': "Content is required for data extraction"
            }
        
        if not selectors:
            return {
                'status': 'error',
                'message': "Selectors are required for data extraction"
            }
        
        try:
            # Import BeautifulSoup
            from bs4 import BeautifulSoup
            
            # Parse the HTML
            soup = BeautifulSoup(content, 'html.parser')
            
            # Extract data
            data = {}
            for key, selector in selectors.items():
                elements = soup.select(selector)
                if elements:
                    data[key] = [element.text.strip() for element in elements]
            
            return {
                'status': 'success',
                'data': data
            }
            
        except ImportError:
            return {
                'status': 'error',
                'message': "BeautifulSoup library not available. Please install it with 'pip install beautifulsoup4'"
            }
        except Exception as e:
            return {
                'status': 'error',
                'message': f"Error extracting data: {str(e)}"
            }
    
    def save_to_csv(self, params):
        """
        Save data to a CSV file.
        
        Args:
            params (dict): Parameters for saving
                - data (list): List of dictionaries to save
                - file_path (str): Path to the CSV file
                - delimiter (str, optional): Delimiter to use
                
        Returns:
            dict: Saving result
        """
        # Extract parameters
        data = params.get('data')
        file_path = params.get('file_path')
        delimiter = params.get('delimiter', ',')
        
        if not data:
            return {
                'status': 'error',
                'message': "Data is required for saving to CSV"
            }
        
        if not file_path:
            return {
                'status': 'error',
                'message': "File path is required for saving to CSV"
            }
        
        try:
            # Import csv
            import csv
            
            # Get the fieldnames from the first item
            fieldnames = list(data[0].keys())
            
            # Write to CSV
            with open(file_path, 'w', newline='') as csvfile:
                writer = csv.DictWriter(csvfile, fieldnames=fieldnames, delimiter=delimiter)
                writer.writeheader()
                writer.writerows(data)
            
            return {
                'status': 'success',
                'message': f"Data saved to {file_path}",
                'file_path': file_path,
                'rows': len(data)
            }
            
        except Exception as e:
            return {
                'status': 'error',
                'message': f"Error saving to CSV: {str(e)}"
            }
