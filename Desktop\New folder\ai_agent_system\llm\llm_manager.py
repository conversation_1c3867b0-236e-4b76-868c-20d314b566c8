"""
LLM Manager Module

This module provides a manager for LLM providers.
"""

import logging
from typing import Dict, List, Any, Optional, Union

from ai_agent_system.config.settings import get_default_llm_provider
from ai_agent_system.llm.llm_provider import LLMProvider, OpenAIProvider, AnthropicProvider, HuggingFaceProvider

# Configure logging
logger = logging.getLogger(__name__)

class LLMManager:
    """
    Manager for LLM providers.
    """
    
    def __init__(self):
        """Initialize the LLM manager."""
        # Dictionary to store LLM providers
        self.providers = {}
        
        # Initialize default provider
        self.default_provider_name = get_default_llm_provider()
        self.default_provider = None
        
        # Register built-in providers
        self._register_built_in_providers()
        
        logger.info("LLM Manager initialized")
    
    def _register_built_in_providers(self) -> None:
        """Register built-in LLM providers."""
        try:
            # Register OpenAI provider
            self.register_provider('openai', OpenAIProvider)
            
            # Register Anthropic provider
            self.register_provider('anthropic', AnthropicProvider)
            
            # Register Hugging Face provider
            self.register_provider('huggingface', HuggingFaceProvider)
            
        except Exception as e:
            logger.error(f"Error registering built-in providers: {e}")
    
    def register_provider(self, provider_name: str, provider_class: Any) -> None:
        """
        Register an LLM provider.
        
        Args:
            provider_name (str): Name of the provider
            provider_class (type): Provider class
        """
        try:
            # Create an instance of the provider
            provider = provider_class()
            
            # Register the provider
            self.providers[provider_name] = provider
            
            # Set as default provider if no default is set
            if self.default_provider is None and (self.default_provider_name is None or self.default_provider_name == provider_name):
                self.default_provider = provider
                self.default_provider_name = provider_name
            
            logger.info(f"Registered LLM provider: {provider_name}")
            
        except Exception as e:
            logger.error(f"Error registering provider {provider_name}: {e}")
    
    def get_provider(self, provider_name: Optional[str] = None) -> LLMProvider:
        """
        Get an LLM provider.
        
        Args:
            provider_name (str, optional): Name of the provider. If not provided, returns the default provider.
            
        Returns:
            LLMProvider: The LLM provider
        """
        if provider_name is None:
            if self.default_provider is None:
                raise ValueError("No default LLM provider available")
            return self.default_provider
        
        if provider_name not in self.providers:
            raise ValueError(f"Unknown LLM provider: {provider_name}")
        
        return self.providers[provider_name]
    
    def get_available_providers(self) -> List[str]:
        """
        Get a list of available LLM providers.
        
        Returns:
            list: List of available LLM providers
        """
        return list(self.providers.keys())
    
    def set_default_provider(self, provider_name: str) -> None:
        """
        Set the default LLM provider.
        
        Args:
            provider_name (str): Name of the provider
        """
        if provider_name not in self.providers:
            raise ValueError(f"Unknown LLM provider: {provider_name}")
        
        self.default_provider = self.providers[provider_name]
        self.default_provider_name = provider_name
        
        logger.info(f"Set default LLM provider: {provider_name}")
    
    def generate_text(self, prompt: str, provider_name: Optional[str] = None, options: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        Generate text using an LLM provider.
        
        Args:
            prompt (str): The prompt to send to the LLM
            provider_name (str, optional): Name of the provider to use. If not provided, uses the default provider.
            options (dict, optional): Additional options for the LLM
            
        Returns:
            dict: The generated text and metadata
        """
        provider = self.get_provider(provider_name)
        return provider.generate_text(prompt, options)
    
    def generate_code(self, prompt: str, language: Optional[str] = None, provider_name: Optional[str] = None, options: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        Generate code using an LLM provider.
        
        Args:
            prompt (str): The prompt to send to the LLM
            language (str, optional): The programming language to generate
            provider_name (str, optional): Name of the provider to use. If not provided, uses the default provider.
            options (dict, optional): Additional options for the LLM
            
        Returns:
            dict: The generated code and metadata
        """
        provider = self.get_provider(provider_name)
        return provider.generate_code(prompt, language, options)
    
    def generate_agent_code(self, agent_type: str, capabilities: List[str], requirements: Optional[Dict[str, Any]] = None, provider_name: Optional[str] = None, options: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        Generate code for an agent using an LLM provider.
        
        Args:
            agent_type (str): The type of agent to generate
            capabilities (list): List of capabilities the agent should have
            requirements (dict, optional): Specific requirements for the agent
            provider_name (str, optional): Name of the provider to use. If not provided, uses the default provider.
            options (dict, optional): Additional options for the LLM
            
        Returns:
            dict: The generated agent code and metadata
        """
        provider = self.get_provider(provider_name)
        return provider.generate_agent_code(agent_type, capabilities, requirements, options)
