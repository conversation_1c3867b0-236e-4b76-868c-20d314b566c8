"""
LangChain Integration Module

This module provides integration with LangChain for advanced LLM capabilities.
"""

import os
import logging
from typing import Dict, List, Any, Optional, Union

# Import LangChain components
try:
    from langchain.llms import OpenAI as LangChainOpenAI
    from langchain.chat_models import ChatOpenAI
    from langchain.schema import HumanMessage, SystemMessage, AIMessage
    from langchain.chains import LL<PERSON>hain, ConversationChain
    from langchain.memory import ConversationBufferMemory
    from langchain.prompts import PromptTemplate, ChatPromptTemplate, HumanMessagePromptTemplate, SystemMessagePromptTemplate
    from langchain.agents import Tool, AgentExecutor, LLMSingleActionAgent, AgentOutputParser
    from langchain.agents import initialize_agent, AgentType
    from langchain.tools import BaseTool
    from langchain_openai import OpenAI, ChatOpenAI
    langchain_available = True
except ImportError:
    langchain_available = False

# Import settings
from ai_agent_system.config.settings import get_llm_provider_settings

# Configure logging
logger = logging.getLogger(__name__)

class LangChainIntegration:
    """
    Integration with LangChain for advanced LLM capabilities.
    """

    def __init__(self, provider: str = 'openai', model: Optional[str] = None):
        """
        Initialize the LangChain integration.

        Args:
            provider (str, optional): The LLM provider to use. Default is 'openai'.
            model (str, optional): The model to use. If not provided, uses the default model for the provider.
        """
        if not langchain_available:
            raise ImportError("LangChain is not available. Please install it with 'pip install langchain langchain-openai'")

        # Get provider settings
        settings = get_llm_provider_settings(provider)

        self.provider = provider
        self.model = model or settings['model']
        self.api_key = settings['api_key']

        # Initialize LangChain components
        self._initialize_langchain()

        logger.info(f"LangChain integration initialized with provider: {provider}, model: {self.model}")

    def _initialize_langchain(self) -> None:
        """Initialize LangChain components."""
        try:
            if self.provider == 'openai':
                # Initialize OpenAI LLM
                self.llm = OpenAI(
                    model_name=self.model,
                    openai_api_key=self.api_key,
                    temperature=0.7
                )

                # Initialize OpenAI Chat model
                self.chat_model = ChatOpenAI(
                    model_name=self.model,
                    openai_api_key=self.api_key,
                    temperature=0.7
                )

                logger.info(f"LangChain OpenAI models initialized: {self.model}")

            elif self.provider == 'anthropic':
                # Anthropic support in LangChain is more limited
                logger.warning("Anthropic support in LangChain is limited. Some features may not be available.")

                # Import Anthropic-specific components
                try:
                    from langchain.chat_models import ChatAnthropic

                    # Initialize Anthropic Chat model
                    self.chat_model = ChatAnthropic(
                        model=self.model,
                        anthropic_api_key=self.api_key,
                        temperature=0.7
                    )

                    # Use chat model as LLM for consistency
                    self.llm = self.chat_model

                    logger.info(f"LangChain Anthropic models initialized: {self.model}")

                except ImportError:
                    logger.error("ChatAnthropic not available in LangChain. Please install the latest version.")
                    raise

            else:
                raise ValueError(f"Unsupported provider for LangChain integration: {self.provider}")

        except Exception as e:
            logger.error(f"Error initializing LangChain components: {e}")
            raise

    def create_chain(self, prompt_template: str, output_key: str = 'text'):
        """
        Create a LangChain LLMChain.

        Args:
            prompt_template (str): The prompt template to use
            output_key (str, optional): The key to use for the output. Default is 'text'.

        Returns:
            LLMChain: The created chain
        """
        prompt = PromptTemplate(template=prompt_template, input_variables=["input"])
        chain = LLMChain(llm=self.llm, prompt=prompt, output_key=output_key)
        return chain

    def create_conversation_chain(self, system_message: Optional[str] = None):
        """
        Create a LangChain ConversationChain.

        Args:
            system_message (str, optional): The system message to use. If not provided, uses a default message.

        Returns:
            ConversationChain: The created conversation chain
        """
        memory = ConversationBufferMemory()

        if system_message:
            # Create a custom prompt template with system message
            prompt = ChatPromptTemplate.from_messages([
                SystemMessagePromptTemplate.from_template(system_message),
                HumanMessagePromptTemplate.from_template("{input}"),
                AIMessage(content="{history}")
            ])

            chain = ConversationChain(
                llm=self.chat_model,
                memory=memory,
                prompt=prompt
            )
        else:
            # Use default ConversationChain
            chain = ConversationChain(
                llm=self.chat_model,
                memory=memory
            )

        return chain

    def create_agent(self, tools: List, system_message: Optional[str] = None):
        """
        Create a LangChain agent.

        Args:
            tools (list): List of tools for the agent to use
            system_message (str, optional): The system message to use. If not provided, uses a default message.

        Returns:
            AgentExecutor: The created agent executor
        """
        # Initialize the agent
        agent = initialize_agent(
            tools=tools,
            llm=self.chat_model,
            agent=AgentType.CHAT_ZERO_SHOT_REACT_DESCRIPTION,
            verbose=True
        )

        # Set system message if provided
        if system_message:
            agent.agent.llm_chain.prompt.messages[0].prompt.template = system_message

        return agent

    def run_chain(self, chain, input_text: str) -> Dict[str, Any]:
        """
        Run a LangChain chain.

        Args:
            chain (LLMChain or ConversationChain): The chain to run
            input_text (str): The input text for the chain

        Returns:
            dict: The output of the chain
        """
        try:
            result = chain.run(input_text)
            return {
                "status": "success",
                "result": result
            }
        except Exception as e:
            logger.error(f"Error running chain: {e}")
            return {
                "status": "error",
                "message": str(e)
            }

    def run_agent(self, agent, input_text: str) -> Dict[str, Any]:
        """
        Run a LangChain agent.

        Args:
            agent (AgentExecutor): The agent to run
            input_text (str): The input text for the agent

        Returns:
            dict: The output of the agent
        """
        try:
            result = agent.run(input_text)
            return {
                "status": "success",
                "result": result
            }
        except Exception as e:
            logger.error(f"Error running agent: {e}")
            return {
                "status": "error",
                "message": str(e)
            }
