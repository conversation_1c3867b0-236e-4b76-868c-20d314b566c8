"""
Dataset Provider Module

This module provides interfaces to various dataset providers.
"""

import os
import json
import logging
from abc import ABC, abstractmethod
from typing import Dict, List, Any, Optional, Union

# Import settings
from ai_agent_system.config.settings import get_dataset_provider_settings

# Configure logging
logger = logging.getLogger(__name__)

class DatasetProvider(ABC):
    """Abstract base class for dataset providers."""
    
    @abstractmethod
    def list_datasets(self, query: Optional[str] = None, limit: int = 10) -> Dict[str, Any]:
        """
        List available datasets.
        
        Args:
            query (str, optional): Query to search for datasets
            limit (int, optional): Maximum number of datasets to return
            
        Returns:
            dict: List of datasets and metadata
        """
        pass
    
    @abstractmethod
    def get_dataset(self, dataset_id: str) -> Dict[str, Any]:
        """
        Get a dataset by ID.
        
        Args:
            dataset_id (str): ID of the dataset
            
        Returns:
            dict: The dataset and metadata
        """
        pass
    
    @abstractmethod
    def query_dataset(self, dataset_id: str, query: str) -> Dict[str, Any]:
        """
        Query a dataset.
        
        Args:
            dataset_id (str): ID of the dataset
            query (str): Query to run on the dataset
            
        Returns:
            dict: Query results and metadata
        """
        pass

class KaggleProvider(DatasetProvider):
    """Kaggle dataset provider implementation."""
    
    def __init__(self, username: Optional[str] = None, key: Optional[str] = None):
        """
        Initialize the Kaggle provider.
        
        Args:
            username (str, optional): Kaggle username. If not provided, uses the settings.
            key (str, optional): Kaggle API key. If not provided, uses the settings.
        """
        # Get settings
        settings = get_dataset_provider_settings('kaggle')
        
        self.username = username or settings['username']
        self.key = key or settings['key']
        
        if not self.username or not self.key:
            raise ValueError("Kaggle username and key not provided and not found in settings")
        
        # Set environment variables for Kaggle API
        os.environ['KAGGLE_USERNAME'] = self.username
        os.environ['KAGGLE_KEY'] = self.key
        
        # Initialize Kaggle API
        try:
            from kaggle.api.kaggle_api_extended import KaggleApi
            self.api = KaggleApi()
            self.api.authenticate()
            logger.info("Kaggle API authenticated")
        except ImportError:
            logger.error("Kaggle API not available. Please install it with 'pip install kaggle'")
            raise
        except Exception as e:
            logger.error(f"Error authenticating with Kaggle API: {e}")
            raise
    
    def list_datasets(self, query: Optional[str] = None, limit: int = 10) -> Dict[str, Any]:
        """
        List available datasets on Kaggle.
        
        Args:
            query (str, optional): Query to search for datasets
            limit (int, optional): Maximum number of datasets to return
            
        Returns:
            dict: List of datasets and metadata
        """
        try:
            # Search for datasets
            datasets = self.api.dataset_list(search=query, page_size=limit)
            
            # Convert to list of dictionaries
            dataset_list = []
            for dataset in datasets:
                dataset_list.append({
                    'id': dataset.ref,
                    'title': dataset.title,
                    'size': dataset.size,
                    'lastUpdated': dataset.lastUpdated,
                    'downloadCount': dataset.downloadCount,
                    'voteCount': dataset.voteCount,
                    'usabilityRating': dataset.usabilityRating
                })
            
            return {
                'status': 'success',
                'datasets': dataset_list,
                'count': len(dataset_list)
            }
            
        except Exception as e:
            logger.error(f"Error listing Kaggle datasets: {e}")
            return {
                'status': 'error',
                'message': str(e)
            }
    
    def get_dataset(self, dataset_id: str) -> Dict[str, Any]:
        """
        Get a dataset from Kaggle.
        
        Args:
            dataset_id (str): ID of the dataset (format: username/dataset-name)
            
        Returns:
            dict: The dataset and metadata
        """
        try:
            # Download the dataset
            self.api.dataset_download_files(dataset_id, path='./data', unzip=True)
            
            # Get dataset metadata
            owner, name = dataset_id.split('/')
            dataset = self.api.dataset_view(owner, name)
            
            # List files in the dataset
            files = os.listdir(f'./data/{name}')
            
            return {
                'status': 'success',
                'dataset': {
                    'id': dataset.ref,
                    'title': dataset.title,
                    'size': dataset.size,
                    'lastUpdated': dataset.lastUpdated,
                    'files': files,
                    'path': f'./data/{name}'
                }
            }
            
        except Exception as e:
            logger.error(f"Error getting Kaggle dataset: {e}")
            return {
                'status': 'error',
                'message': str(e)
            }
    
    def query_dataset(self, dataset_id: str, query: str) -> Dict[str, Any]:
        """
        Query a dataset from Kaggle.
        
        Args:
            dataset_id (str): ID of the dataset (format: username/dataset-name)
            query (str): Query to run on the dataset
            
        Returns:
            dict: Query results and metadata
        """
        try:
            # Get the dataset
            result = self.get_dataset(dataset_id)
            
            if result['status'] != 'success':
                return result
            
            dataset = result['dataset']
            
            # Find CSV files in the dataset
            csv_files = [f for f in dataset['files'] if f.endswith('.csv')]
            
            if not csv_files:
                return {
                    'status': 'error',
                    'message': 'No CSV files found in the dataset'
                }
            
            # Load the first CSV file
            import pandas as pd
            df = pd.read_csv(f"{dataset['path']}/{csv_files[0]}")
            
            # Parse the query
            # This is a simple implementation that supports basic filtering
            # For example: "column == value" or "column > value"
            try:
                filtered_df = df.query(query)
                
                return {
                    'status': 'success',
                    'results': filtered_df.to_dict(orient='records'),
                    'count': len(filtered_df),
                    'columns': list(filtered_df.columns)
                }
                
            except Exception as e:
                logger.error(f"Error querying dataset: {e}")
                return {
                    'status': 'error',
                    'message': f"Error querying dataset: {str(e)}"
                }
            
        except Exception as e:
            logger.error(f"Error querying Kaggle dataset: {e}")
            return {
                'status': 'error',
                'message': str(e)
            }

class BigQueryProvider(DatasetProvider):
    """Google BigQuery dataset provider implementation."""
    
    def __init__(self, credentials_path: Optional[str] = None):
        """
        Initialize the BigQuery provider.
        
        Args:
            credentials_path (str, optional): Path to Google Cloud credentials file. If not provided, uses the settings.
        """
        # Get settings
        settings = get_dataset_provider_settings('bigquery')
        
        self.credentials_path = credentials_path or settings['credentials_path']
        
        if not self.credentials_path:
            raise ValueError("Google Cloud credentials path not provided and not found in settings")
        
        # Set environment variable for Google Cloud credentials
        os.environ['GOOGLE_APPLICATION_CREDENTIALS'] = self.credentials_path
        
        # Initialize BigQuery client
        try:
            from google.cloud import bigquery
            self.client = bigquery.Client()
            logger.info("BigQuery client initialized")
        except ImportError:
            logger.error("BigQuery client not available. Please install it with 'pip install google-cloud-bigquery'")
            raise
        except Exception as e:
            logger.error(f"Error initializing BigQuery client: {e}")
            raise
    
    def list_datasets(self, query: Optional[str] = None, limit: int = 10) -> Dict[str, Any]:
        """
        List available datasets in BigQuery.
        
        Args:
            query (str, optional): Query to search for datasets
            limit (int, optional): Maximum number of datasets to return
            
        Returns:
            dict: List of datasets and metadata
        """
        try:
            # List datasets
            datasets = list(self.client.list_datasets())
            
            # Filter datasets if query is provided
            if query:
                datasets = [d for d in datasets if query.lower() in d.dataset_id.lower()]
            
            # Limit the number of datasets
            datasets = datasets[:limit]
            
            # Convert to list of dictionaries
            dataset_list = []
            for dataset in datasets:
                dataset_list.append({
                    'id': dataset.dataset_id,
                    'project': dataset.project,
                    'path': f"{dataset.project}.{dataset.dataset_id}",
                    'location': dataset.location
                })
            
            return {
                'status': 'success',
                'datasets': dataset_list,
                'count': len(dataset_list)
            }
            
        except Exception as e:
            logger.error(f"Error listing BigQuery datasets: {e}")
            return {
                'status': 'error',
                'message': str(e)
            }
    
    def get_dataset(self, dataset_id: str) -> Dict[str, Any]:
        """
        Get a dataset from BigQuery.
        
        Args:
            dataset_id (str): ID of the dataset (format: project.dataset)
            
        Returns:
            dict: The dataset and metadata
        """
        try:
            # Parse dataset ID
            project, dataset = dataset_id.split('.')
            
            # Get dataset reference
            dataset_ref = self.client.dataset(dataset, project=project)
            
            # Get dataset
            dataset = self.client.get_dataset(dataset_ref)
            
            # List tables in the dataset
            tables = list(self.client.list_tables(dataset))
            
            # Convert to list of dictionaries
            table_list = []
            for table in tables:
                table_list.append({
                    'id': table.table_id,
                    'project': table.project,
                    'dataset': table.dataset_id,
                    'path': f"{table.project}.{table.dataset_id}.{table.table_id}"
                })
            
            return {
                'status': 'success',
                'dataset': {
                    'id': dataset.dataset_id,
                    'project': dataset.project,
                    'location': dataset.location,
                    'description': dataset.description,
                    'tables': table_list,
                    'table_count': len(table_list)
                }
            }
            
        except Exception as e:
            logger.error(f"Error getting BigQuery dataset: {e}")
            return {
                'status': 'error',
                'message': str(e)
            }
    
    def query_dataset(self, dataset_id: str, query: str) -> Dict[str, Any]:
        """
        Query a dataset from BigQuery.
        
        Args:
            dataset_id (str): ID of the dataset (format: project.dataset)
            query (str): SQL query to run on the dataset
            
        Returns:
            dict: Query results and metadata
        """
        try:
            # Run the query
            query_job = self.client.query(query)
            
            # Wait for the query to finish
            results = query_job.result()
            
            # Convert to list of dictionaries
            rows = []
            for row in results:
                rows.append(dict(row.items()))
            
            # Get schema
            schema = [field.name for field in results.schema]
            
            return {
                'status': 'success',
                'results': rows,
                'count': len(rows),
                'columns': schema,
                'bytes_processed': query_job.total_bytes_processed,
                'job_id': query_job.job_id
            }
            
        except Exception as e:
            logger.error(f"Error querying BigQuery dataset: {e}")
            return {
                'status': 'error',
                'message': str(e)
            }

class S3Provider(DatasetProvider):
    """AWS S3 dataset provider implementation."""
    
    def __init__(self, access_key_id: Optional[str] = None, secret_access_key: Optional[str] = None, region: Optional[str] = None):
        """
        Initialize the S3 provider.
        
        Args:
            access_key_id (str, optional): AWS access key ID. If not provided, uses the settings.
            secret_access_key (str, optional): AWS secret access key. If not provided, uses the settings.
            region (str, optional): AWS region. If not provided, uses the settings.
        """
        # Get settings
        settings = get_dataset_provider_settings('s3')
        
        self.access_key_id = access_key_id or settings['access_key_id']
        self.secret_access_key = secret_access_key or settings['secret_access_key']
        self.region = region or settings['region']
        
        if not self.access_key_id or not self.secret_access_key:
            raise ValueError("AWS access key ID and secret access key not provided and not found in settings")
        
        # Initialize S3 client
        try:
            import boto3
            self.s3 = boto3.client(
                's3',
                aws_access_key_id=self.access_key_id,
                aws_secret_access_key=self.secret_access_key,
                region_name=self.region
            )
            logger.info("S3 client initialized")
        except ImportError:
            logger.error("Boto3 not available. Please install it with 'pip install boto3'")
            raise
        except Exception as e:
            logger.error(f"Error initializing S3 client: {e}")
            raise
    
    def list_datasets(self, query: Optional[str] = None, limit: int = 10) -> Dict[str, Any]:
        """
        List available buckets in S3.
        
        Args:
            query (str, optional): Query to search for buckets
            limit (int, optional): Maximum number of buckets to return
            
        Returns:
            dict: List of buckets and metadata
        """
        try:
            # List buckets
            response = self.s3.list_buckets()
            
            # Filter buckets if query is provided
            buckets = response['Buckets']
            if query:
                buckets = [b for b in buckets if query.lower() in b['Name'].lower()]
            
            # Limit the number of buckets
            buckets = buckets[:limit]
            
            # Convert to list of dictionaries
            bucket_list = []
            for bucket in buckets:
                bucket_list.append({
                    'name': bucket['Name'],
                    'creation_date': bucket['CreationDate'].isoformat()
                })
            
            return {
                'status': 'success',
                'buckets': bucket_list,
                'count': len(bucket_list)
            }
            
        except Exception as e:
            logger.error(f"Error listing S3 buckets: {e}")
            return {
                'status': 'error',
                'message': str(e)
            }
    
    def get_dataset(self, dataset_id: str) -> Dict[str, Any]:
        """
        Get a bucket from S3.
        
        Args:
            dataset_id (str): ID of the bucket (bucket name)
            
        Returns:
            dict: The bucket and metadata
        """
        try:
            # List objects in the bucket
            response = self.s3.list_objects_v2(Bucket=dataset_id)
            
            # Check if the bucket exists
            if 'Contents' not in response:
                return {
                    'status': 'error',
                    'message': f"Bucket not found: {dataset_id}"
                }
            
            # Convert to list of dictionaries
            object_list = []
            for obj in response['Contents']:
                object_list.append({
                    'key': obj['Key'],
                    'size': obj['Size'],
                    'last_modified': obj['LastModified'].isoformat()
                })
            
            return {
                'status': 'success',
                'bucket': {
                    'name': dataset_id,
                    'objects': object_list,
                    'object_count': len(object_list)
                }
            }
            
        except Exception as e:
            logger.error(f"Error getting S3 bucket: {e}")
            return {
                'status': 'error',
                'message': str(e)
            }
    
    def query_dataset(self, dataset_id: str, query: str) -> Dict[str, Any]:
        """
        Query a bucket from S3.
        
        Args:
            dataset_id (str): ID of the bucket (bucket name)
            query (str): Query to run on the bucket (object key or prefix)
            
        Returns:
            dict: Query results and metadata
        """
        try:
            # Check if the query is a specific object key
            if '/' in query:
                # Get the object
                response = self.s3.get_object(Bucket=dataset_id, Key=query)
                
                # Read the object content
                content = response['Body'].read()
                
                # Determine the file type
                if query.endswith('.csv'):
                    # Parse CSV
                    import pandas as pd
                    import io
                    df = pd.read_csv(io.BytesIO(content))
                    
                    return {
                        'status': 'success',
                        'results': df.to_dict(orient='records'),
                        'count': len(df),
                        'columns': list(df.columns),
                        'file_type': 'csv'
                    }
                    
                elif query.endswith('.json'):
                    # Parse JSON
                    import json
                    data = json.loads(content)
                    
                    return {
                        'status': 'success',
                        'results': data,
                        'file_type': 'json'
                    }
                    
                else:
                    # Return raw content
                    return {
                        'status': 'success',
                        'content': content.decode('utf-8'),
                        'content_type': response['ContentType'],
                        'content_length': response['ContentLength'],
                        'file_type': 'raw'
                    }
                    
            else:
                # List objects with the prefix
                response = self.s3.list_objects_v2(Bucket=dataset_id, Prefix=query)
                
                # Check if any objects match the prefix
                if 'Contents' not in response:
                    return {
                        'status': 'error',
                        'message': f"No objects found with prefix: {query}"
                    }
                
                # Convert to list of dictionaries
                object_list = []
                for obj in response['Contents']:
                    object_list.append({
                        'key': obj['Key'],
                        'size': obj['Size'],
                        'last_modified': obj['LastModified'].isoformat()
                    })
                
                return {
                    'status': 'success',
                    'results': object_list,
                    'count': len(object_list),
                    'prefix': query
                }
                
        except Exception as e:
            logger.error(f"Error querying S3 bucket: {e}")
            return {
                'status': 'error',
                'message': str(e)
            }
