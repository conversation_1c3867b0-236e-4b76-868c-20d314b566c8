"""
Tests for the BaseAgent class.
"""

import os
import unittest
from unittest.mock import patch

from ai_agent_system.core.base_agent import BaseAgent

class TestBaseAgent(unittest.TestCase):
    """Tests for the BaseAgent class."""
    
    def setUp(self):
        """Set up test fixtures."""
        # Create logs directory if it doesn't exist
        os.makedirs('logs/agents', exist_ok=True)
        
        # Create a base agent
        self.agent = BaseAgent(agent_id='test_agent', agent_type='test', capabilities=['test_capability'])
    
    def test_init(self):
        """Test initialization of the BaseAgent class."""
        self.assertEqual(self.agent.agent_id, 'test_agent')
        self.assertEqual(self.agent.agent_type, 'test')
        self.assertEqual(self.agent.capabilities, ['test_capability'])
        self.assertEqual(self.agent.status, 'initialized')
        self.assertEqual(self.agent.performance_metrics['tasks_completed'], 0)
        self.assertEqual(self.agent.performance_metrics['success_rate'], 0.0)
        self.assertEqual(self.agent.performance_metrics['average_response_time'], 0.0)
        self.assertEqual(len(self.agent.task_history), 0)
    
    def test_execute_task_with_capability(self):
        """Test executing a task with a capability."""
        # Add a test method to the agent
        def test_capability(self, params):
            return {
                'status': 'success',
                'message': 'Test capability executed',
                'params': params
            }
        
        # Add the method to the agent
        self.agent.test_capability = test_capability.__get__(self.agent, BaseAgent)
        
        # Execute the task
        result = self.agent.execute_task('test_capability', {'test_param': 'test_value'})
        
        # Check the result
        self.assertEqual(result['status'], 'success')
        self.assertEqual(result['message'], 'Test capability executed')
        self.assertEqual(result['params'], {'test_param': 'test_value'})
        
        # Check that the task was recorded
        self.assertEqual(self.agent.performance_metrics['tasks_completed'], 1)
        self.assertEqual(self.agent.performance_metrics['success_rate'], 1.0)
        self.assertGreater(self.agent.performance_metrics['average_response_time'], 0.0)
        self.assertEqual(len(self.agent.task_history), 1)
        self.assertEqual(self.agent.task_history[0]['task_name'], 'test_capability')
        self.assertEqual(self.agent.task_history[0]['task_params'], {'test_param': 'test_value'})
        self.assertEqual(self.agent.task_history[0]['result_status'], 'success')
    
    def test_execute_task_without_capability(self):
        """Test executing a task without a capability."""
        # Execute a task that the agent doesn't have a capability for
        result = self.agent.execute_task('unknown_capability')
        
        # Check the result
        self.assertEqual(result['status'], 'error')
        self.assertEqual(result['message'], 'Agent does not have capability: unknown_capability')
        
        # Check that the task was not recorded
        self.assertEqual(self.agent.performance_metrics['tasks_completed'], 0)
        self.assertEqual(self.agent.performance_metrics['success_rate'], 0.0)
        self.assertEqual(self.agent.performance_metrics['average_response_time'], 0.0)
        self.assertEqual(len(self.agent.task_history), 0)
    
    def test_execute_task_with_error(self):
        """Test executing a task that raises an error."""
        # Add a test method to the agent that raises an error
        def test_capability_error(self, params):
            raise ValueError('Test error')
        
        # Add the method to the agent
        self.agent.test_capability = test_capability_error.__get__(self.agent, BaseAgent)
        
        # Execute the task
        result = self.agent.execute_task('test_capability')
        
        # Check the result
        self.assertEqual(result['status'], 'error')
        self.assertEqual(result['message'], 'Error executing task: Test error')
        
        # Check that the task was not recorded
        self.assertEqual(self.agent.performance_metrics['tasks_completed'], 0)
        self.assertEqual(self.agent.performance_metrics['success_rate'], 0.0)
        self.assertEqual(self.agent.performance_metrics['average_response_time'], 0.0)
        self.assertEqual(len(self.agent.task_history), 0)
    
    def test_default_task_execution(self):
        """Test the default task execution method."""
        # Add the capability to the agent
        self.agent.capabilities.append('default_task')
        
        # Execute the task
        result = self.agent.execute_task('default_task', {'test_param': 'test_value'})
        
        # Check the result
        self.assertEqual(result['status'], 'success')
        self.assertEqual(result['message'], 'Executed task default_task with default implementation')
        self.assertEqual(result['task_name'], 'default_task')
        self.assertEqual(result['task_params'], {'test_param': 'test_value'})
        
        # Check that the task was recorded
        self.assertEqual(self.agent.performance_metrics['tasks_completed'], 1)
        self.assertEqual(self.agent.performance_metrics['success_rate'], 1.0)
        self.assertGreater(self.agent.performance_metrics['average_response_time'], 0.0)
        self.assertEqual(len(self.agent.task_history), 1)
        self.assertEqual(self.agent.task_history[0]['task_name'], 'default_task')
        self.assertEqual(self.agent.task_history[0]['task_params'], {'test_param': 'test_value'})
        self.assertEqual(self.agent.task_history[0]['result_status'], 'success')
    
    def test_get_status(self):
        """Test getting the agent status."""
        # Get the agent status
        status = self.agent.get_status()
        
        # Check the status
        self.assertEqual(status['agent_id'], 'test_agent')
        self.assertEqual(status['agent_type'], 'test')
        self.assertEqual(status['status'], 'initialized')
        self.assertEqual(status['capabilities'], ['test_capability'])
        self.assertEqual(status['performance_metrics']['tasks_completed'], 0)
        self.assertEqual(status['performance_metrics']['success_rate'], 0.0)
        self.assertEqual(status['performance_metrics']['average_response_time'], 0.0)
    
    def test_get_task_history(self):
        """Test getting the task history."""
        # Add a task to the history
        self.agent.task_history.append({
            'task_name': 'test_task',
            'task_params': {'test_param': 'test_value'},
            'result_status': 'success',
            'timestamp': '2021-01-01T00:00:00'
        })
        
        # Get the task history
        history = self.agent.get_task_history()
        
        # Check the history
        self.assertEqual(len(history), 1)
        self.assertEqual(history[0]['task_name'], 'test_task')
        self.assertEqual(history[0]['task_params'], {'test_param': 'test_value'})
        self.assertEqual(history[0]['result_status'], 'success')
        self.assertEqual(history[0]['timestamp'], '2021-01-01T00:00:00')
    
    def test_add_capability(self):
        """Test adding a capability."""
        # Add a capability
        result = self.agent.add_capability('new_capability')
        
        # Check the result
        self.assertTrue(result)
        self.assertIn('new_capability', self.agent.capabilities)
        
        # Try to add the same capability again
        result = self.agent.add_capability('new_capability')
        
        # Check the result
        self.assertFalse(result)
    
    def test_remove_capability(self):
        """Test removing a capability."""
        # Remove a capability
        result = self.agent.remove_capability('test_capability')
        
        # Check the result
        self.assertTrue(result)
        self.assertNotIn('test_capability', self.agent.capabilities)
        
        # Try to remove a capability that doesn't exist
        result = self.agent.remove_capability('unknown_capability')
        
        # Check the result
        self.assertFalse(result)
    
    def test_str_and_repr(self):
        """Test string and representation methods."""
        # Check string representation
        self.assertEqual(str(self.agent), 'Test Agent (ID: test_agent)')
        
        # Check detailed representation
        self.assertEqual(repr(self.agent), "TestAgent(agent_id='test_agent', capabilities=['test_capability'])")

if __name__ == '__main__':
    unittest.main()
