"""
Tests for the Coordinator class.
"""

import os
import unittest
from unittest.mock import patch, MagicMock

from ai_agent_system.core.coordinator import Coordinator
from ai_agent_system.core.base_agent import BaseAgent

class TestCoordinator(unittest.TestCase):
    """Tests for the Coordinator class."""
    
    def setUp(self):
        """Set up test fixtures."""
        # Create logs directory if it doesn't exist
        os.makedirs('logs/coordinators', exist_ok=True)
        
        # Create a coordinator
        self.coordinator = Coordinator(coordinator_id='test_coordinator')
        
        # Mock the agent factory
        self.coordinator.agent_factory = MagicMock()
        
        # Create a mock agent
        self.mock_agent = MagicMock(spec=BaseAgent)
        self.mock_agent.agent_id = 'test_agent'
        self.mock_agent.agent_type = 'test'
        self.mock_agent.capabilities = ['test_capability']
        self.mock_agent.get_status.return_value = {
            'agent_id': 'test_agent',
            'agent_type': 'test',
            'status': 'initialized',
            'capabilities': ['test_capability']
        }
        
        # Set up the agent factory to return the mock agent
        self.coordinator.agent_factory.create_agent.return_value = self.mock_agent
    
    def test_init(self):
        """Test initialization of the Coordinator class."""
        self.assertEqual(self.coordinator.coordinator_id, 'test_coordinator')
        self.assertEqual(self.coordinator.status, 'initialized')
        self.assertEqual(len(self.coordinator.agents), 0)
        self.assertEqual(len(self.coordinator.agent_groups), 0)
        self.assertEqual(len(self.coordinator.tasks), 0)
    
    def test_create_agent(self):
        """Test creating an agent."""
        # Create an agent
        agent = self.coordinator.create_agent('test', 'test_agent', ['test_capability'])
        
        # Check that the agent factory was called
        self.coordinator.agent_factory.create_agent.assert_called_once_with('test', 'test_agent', ['test_capability'])
        
        # Check that the agent was stored
        self.assertEqual(len(self.coordinator.agents), 1)
        self.assertEqual(self.coordinator.agents['test_agent'], self.mock_agent)
        
        # Check that the agent was returned
        self.assertEqual(agent, self.mock_agent)
    
    def test_get_agent(self):
        """Test getting an agent."""
        # Create an agent
        self.coordinator.create_agent('test', 'test_agent', ['test_capability'])
        
        # Get the agent
        agent = self.coordinator.get_agent('test_agent')
        
        # Check that the agent was returned
        self.assertEqual(agent, self.mock_agent)
        
        # Try to get an agent that doesn't exist
        agent = self.coordinator.get_agent('unknown_agent')
        
        # Check that None was returned
        self.assertIsNone(agent)
    
    def test_get_agents(self):
        """Test getting agents."""
        # Create an agent
        self.coordinator.create_agent('test', 'test_agent', ['test_capability'])
        
        # Get all agents
        agents = self.coordinator.get_agents()
        
        # Check that the agent was returned
        self.assertEqual(len(agents), 1)
        self.assertEqual(agents[0], self.mock_agent)
        
        # Get agents by type
        agents = self.coordinator.get_agents('test')
        
        # Check that the agent was returned
        self.assertEqual(len(agents), 1)
        self.assertEqual(agents[0], self.mock_agent)
        
        # Get agents by a different type
        agents = self.coordinator.get_agents('unknown')
        
        # Check that no agents were returned
        self.assertEqual(len(agents), 0)
    
    def test_remove_agent(self):
        """Test removing an agent."""
        # Create an agent
        self.coordinator.create_agent('test', 'test_agent', ['test_capability'])
        
        # Remove the agent
        result = self.coordinator.remove_agent('test_agent')
        
        # Check the result
        self.assertTrue(result)
        
        # Check that the agent was removed
        self.assertEqual(len(self.coordinator.agents), 0)
        
        # Try to remove an agent that doesn't exist
        result = self.coordinator.remove_agent('unknown_agent')
        
        # Check the result
        self.assertFalse(result)
    
    def test_create_agent_group(self):
        """Test creating an agent group."""
        # Create an agent
        self.coordinator.create_agent('test', 'test_agent', ['test_capability'])
        
        # Create a group
        group = self.coordinator.create_agent_group('test_group', 'Test Group', 'Test group description', ['test_agent'])
        
        # Check the group
        self.assertEqual(group['group_id'], 'test_group')
        self.assertEqual(group['name'], 'Test Group')
        self.assertEqual(group['description'], 'Test group description')
        self.assertEqual(group['agents'], ['test_agent'])
        
        # Check that the group was stored
        self.assertEqual(len(self.coordinator.agent_groups), 1)
        self.assertEqual(self.coordinator.agent_groups['test_group'], group)
        
        # Try to create a group with the same ID
        with self.assertRaises(ValueError):
            self.coordinator.create_agent_group('test_group', 'Another Group')
        
        # Try to create a group with an unknown agent
        with self.assertRaises(ValueError):
            self.coordinator.create_agent_group('another_group', 'Another Group', 'Another group', ['unknown_agent'])
    
    def test_get_agent_group(self):
        """Test getting an agent group."""
        # Create a group
        group = self.coordinator.create_agent_group('test_group', 'Test Group')
        
        # Get the group
        result = self.coordinator.get_agent_group('test_group')
        
        # Check the result
        self.assertEqual(result, group)
        
        # Try to get a group that doesn't exist
        result = self.coordinator.get_agent_group('unknown_group')
        
        # Check the result
        self.assertIsNone(result)
    
    def test_get_agent_groups(self):
        """Test getting agent groups."""
        # Create a group
        group = self.coordinator.create_agent_group('test_group', 'Test Group')
        
        # Get all groups
        groups = self.coordinator.get_agent_groups()
        
        # Check the result
        self.assertEqual(len(groups), 1)
        self.assertEqual(groups[0], group)
    
    def test_add_agent_to_group(self):
        """Test adding an agent to a group."""
        # Create an agent
        self.coordinator.create_agent('test', 'test_agent', ['test_capability'])
        
        # Create a group
        self.coordinator.create_agent_group('test_group', 'Test Group')
        
        # Add the agent to the group
        result = self.coordinator.add_agent_to_group('test_group', 'test_agent')
        
        # Check the result
        self.assertTrue(result)
        
        # Check that the agent was added to the group
        self.assertEqual(self.coordinator.agent_groups['test_group']['agents'], ['test_agent'])
        
        # Try to add the agent again
        result = self.coordinator.add_agent_to_group('test_group', 'test_agent')
        
        # Check the result
        self.assertFalse(result)
        
        # Try to add an agent to a group that doesn't exist
        result = self.coordinator.add_agent_to_group('unknown_group', 'test_agent')
        
        # Check the result
        self.assertFalse(result)
        
        # Try to add an agent that doesn't exist
        result = self.coordinator.add_agent_to_group('test_group', 'unknown_agent')
        
        # Check the result
        self.assertFalse(result)
    
    def test_remove_agent_from_group(self):
        """Test removing an agent from a group."""
        # Create an agent
        self.coordinator.create_agent('test', 'test_agent', ['test_capability'])
        
        # Create a group with the agent
        self.coordinator.create_agent_group('test_group', 'Test Group', 'Test group description', ['test_agent'])
        
        # Remove the agent from the group
        result = self.coordinator.remove_agent_from_group('test_group', 'test_agent')
        
        # Check the result
        self.assertTrue(result)
        
        # Check that the agent was removed from the group
        self.assertEqual(self.coordinator.agent_groups['test_group']['agents'], [])
        
        # Try to remove the agent again
        result = self.coordinator.remove_agent_from_group('test_group', 'test_agent')
        
        # Check the result
        self.assertFalse(result)
        
        # Try to remove an agent from a group that doesn't exist
        result = self.coordinator.remove_agent_from_group('unknown_group', 'test_agent')
        
        # Check the result
        self.assertFalse(result)
    
    def test_remove_agent_group(self):
        """Test removing an agent group."""
        # Create a group
        self.coordinator.create_agent_group('test_group', 'Test Group')
        
        # Remove the group
        result = self.coordinator.remove_agent_group('test_group')
        
        # Check the result
        self.assertTrue(result)
        
        # Check that the group was removed
        self.assertEqual(len(self.coordinator.agent_groups), 0)
        
        # Try to remove a group that doesn't exist
        result = self.coordinator.remove_agent_group('unknown_group')
        
        # Check the result
        self.assertFalse(result)
    
    def test_create_task(self):
        """Test creating a task."""
        # Create an agent
        self.coordinator.create_agent('test', 'test_agent', ['test_capability'])
        
        # Create a task for the agent
        task = self.coordinator.create_task('test_task', 'Test task description', 'test_agent', None, {'test_param': 'test_value'})
        
        # Check the task
        self.assertEqual(task['task_name'], 'test_task')
        self.assertEqual(task['task_description'], 'Test task description')
        self.assertEqual(task['agent_id'], 'test_agent')
        self.assertIsNone(task['group_id'])
        self.assertEqual(task['task_params'], {'test_param': 'test_value'})
        self.assertEqual(task['status'], 'created')
        
        # Check that the task was stored
        self.assertEqual(len(self.coordinator.tasks), 1)
        self.assertEqual(self.coordinator.tasks[task['task_id']], task)
        
        # Create a group
        self.coordinator.create_agent_group('test_group', 'Test Group')
        
        # Create a task for the group
        task = self.coordinator.create_task('test_task', 'Test task description', None, 'test_group', {'test_param': 'test_value'})
        
        # Check the task
        self.assertEqual(task['task_name'], 'test_task')
        self.assertEqual(task['task_description'], 'Test task description')
        self.assertIsNone(task['agent_id'])
        self.assertEqual(task['group_id'], 'test_group')
        self.assertEqual(task['task_params'], {'test_param': 'test_value'})
        self.assertEqual(task['status'], 'created')
        
        # Check that the task was stored
        self.assertEqual(len(self.coordinator.tasks), 2)
        self.assertEqual(self.coordinator.tasks[task['task_id']], task)
        
        # Try to create a task without an agent or group
        with self.assertRaises(ValueError):
            self.coordinator.create_task('test_task', 'Test task description')
        
        # Try to create a task with an unknown agent
        with self.assertRaises(ValueError):
            self.coordinator.create_task('test_task', 'Test task description', 'unknown_agent')
        
        # Try to create a task with an unknown group
        with self.assertRaises(ValueError):
            self.coordinator.create_task('test_task', 'Test task description', None, 'unknown_group')
    
    def test_get_task(self):
        """Test getting a task."""
        # Create an agent
        self.coordinator.create_agent('test', 'test_agent', ['test_capability'])
        
        # Create a task
        task = self.coordinator.create_task('test_task', 'Test task description', 'test_agent')
        
        # Get the task
        result = self.coordinator.get_task(task['task_id'])
        
        # Check the result
        self.assertEqual(result, task)
        
        # Try to get a task that doesn't exist
        result = self.coordinator.get_task('unknown_task')
        
        # Check the result
        self.assertIsNone(result)
    
    def test_get_tasks(self):
        """Test getting tasks."""
        # Create an agent
        self.coordinator.create_agent('test', 'test_agent', ['test_capability'])
        
        # Create a task
        task = self.coordinator.create_task('test_task', 'Test task description', 'test_agent')
        
        # Get all tasks
        tasks = self.coordinator.get_tasks()
        
        # Check the result
        self.assertEqual(len(tasks), 1)
        self.assertEqual(tasks[0], task)
        
        # Get tasks by agent
        tasks = self.coordinator.get_tasks('test_agent')
        
        # Check the result
        self.assertEqual(len(tasks), 1)
        self.assertEqual(tasks[0], task)
        
        # Get tasks by a different agent
        tasks = self.coordinator.get_tasks('unknown_agent')
        
        # Check the result
        self.assertEqual(len(tasks), 0)
        
        # Get tasks by status
        tasks = self.coordinator.get_tasks(status='created')
        
        # Check the result
        self.assertEqual(len(tasks), 1)
        self.assertEqual(tasks[0], task)
        
        # Get tasks by a different status
        tasks = self.coordinator.get_tasks(status='completed')
        
        # Check the result
        self.assertEqual(len(tasks), 0)
    
    def test_execute_task_for_agent(self):
        """Test executing a task for an agent."""
        # Create an agent
        self.coordinator.create_agent('test', 'test_agent', ['test_capability'])
        
        # Set up the mock agent to return a success result
        self.mock_agent.execute_task.return_value = {
            'status': 'success',
            'message': 'Task executed successfully'
        }
        
        # Create a task
        task = self.coordinator.create_task('test_task', 'Test task description', 'test_agent', None, {'test_param': 'test_value'})
        
        # Execute the task
        result = self.coordinator.execute_task(task['task_id'])
        
        # Check that the agent's execute_task method was called
        self.mock_agent.execute_task.assert_called_once_with('test_task', {'test_param': 'test_value'})
        
        # Check the result
        self.assertEqual(result['status'], 'completed')
        self.assertEqual(result['result']['status'], 'success')
        self.assertEqual(result['result']['message'], 'Task executed successfully')
        
        # Check that the task was updated
        self.assertEqual(self.coordinator.tasks[task['task_id']]['status'], 'completed')
        self.assertEqual(self.coordinator.tasks[task['task_id']]['result']['status'], 'success')
        self.assertEqual(self.coordinator.tasks[task['task_id']]['result']['message'], 'Task executed successfully')
        self.assertIsNotNone(self.coordinator.tasks[task['task_id']]['completed_at'])
    
    def test_execute_task_for_group(self):
        """Test executing a task for a group."""
        # Create two agents
        self.coordinator.create_agent('test', 'test_agent1', ['test_capability'])
        
        # Create a second mock agent
        mock_agent2 = MagicMock(spec=BaseAgent)
        mock_agent2.agent_id = 'test_agent2'
        mock_agent2.agent_type = 'test'
        mock_agent2.capabilities = ['test_capability']
        
        # Add the second agent to the coordinator
        self.coordinator.agents['test_agent2'] = mock_agent2
        
        # Create a group with both agents
        self.coordinator.create_agent_group('test_group', 'Test Group', 'Test group description', ['test_agent1', 'test_agent2'])
        
        # Set up the mock agents to return success results
        self.mock_agent.execute_task.return_value = {
            'status': 'success',
            'message': 'Task executed successfully by agent 1'
        }
        
        mock_agent2.execute_task.return_value = {
            'status': 'success',
            'message': 'Task executed successfully by agent 2'
        }
        
        # Create a task for the group
        task = self.coordinator.create_task('test_task', 'Test task description', None, 'test_group', {'test_param': 'test_value'})
        
        # Execute the task
        result = self.coordinator.execute_task(task['task_id'])
        
        # Check that both agents' execute_task methods were called
        self.mock_agent.execute_task.assert_called_once_with('test_task', {'test_param': 'test_value'})
        mock_agent2.execute_task.assert_called_once_with('test_task', {'test_param': 'test_value'})
        
        # Check the result
        self.assertEqual(result['status'], 'completed')
        self.assertEqual(result['result']['status'], 'success')
        self.assertEqual(result['result']['message'], 'Task executed on 2 agents')
        self.assertEqual(len(result['result']['agent_results']), 2)
        self.assertEqual(result['result']['agent_results']['test_agent1']['status'], 'success')
        self.assertEqual(result['result']['agent_results']['test_agent1']['message'], 'Task executed successfully by agent 1')
        self.assertEqual(result['result']['agent_results']['test_agent2']['status'], 'success')
        self.assertEqual(result['result']['agent_results']['test_agent2']['message'], 'Task executed successfully by agent 2')
        
        # Check that the task was updated
        self.assertEqual(self.coordinator.tasks[task['task_id']]['status'], 'completed')
        self.assertEqual(self.coordinator.tasks[task['task_id']]['result']['status'], 'success')
        self.assertEqual(self.coordinator.tasks[task['task_id']]['result']['message'], 'Task executed on 2 agents')
        self.assertIsNotNone(self.coordinator.tasks[task['task_id']]['completed_at'])
    
    def test_execute_task_with_error(self):
        """Test executing a task that raises an error."""
        # Create an agent
        self.coordinator.create_agent('test', 'test_agent', ['test_capability'])
        
        # Set up the mock agent to raise an error
        self.mock_agent.execute_task.side_effect = ValueError('Test error')
        
        # Create a task
        task = self.coordinator.create_task('test_task', 'Test task description', 'test_agent')
        
        # Execute the task
        result = self.coordinator.execute_task(task['task_id'])
        
        # Check the result
        self.assertEqual(result['status'], 'error')
        self.assertEqual(result['result']['status'], 'error')
        self.assertEqual(result['result']['message'], 'Error executing task: Test error')
        
        # Check that the task was updated
        self.assertEqual(self.coordinator.tasks[task['task_id']]['status'], 'error')
        self.assertEqual(self.coordinator.tasks[task['task_id']]['result']['status'], 'error')
        self.assertEqual(self.coordinator.tasks[task['task_id']]['result']['message'], 'Error executing task: Test error')
    
    def test_execute_task_that_doesnt_exist(self):
        """Test executing a task that doesn't exist."""
        # Try to execute a task that doesn't exist
        with self.assertRaises(ValueError):
            self.coordinator.execute_task('unknown_task')
    
    def test_cancel_task(self):
        """Test canceling a task."""
        # Create an agent
        self.coordinator.create_agent('test', 'test_agent', ['test_capability'])
        
        # Create a task
        task = self.coordinator.create_task('test_task', 'Test task description', 'test_agent')
        
        # Cancel the task
        result = self.coordinator.cancel_task(task['task_id'])
        
        # Check the result
        self.assertEqual(result['status'], 'cancelled')
        
        # Check that the task was updated
        self.assertEqual(self.coordinator.tasks[task['task_id']]['status'], 'cancelled')
        
        # Try to cancel a task that is already completed
        self.coordinator.tasks[task['task_id']]['status'] = 'completed'
        result = self.coordinator.cancel_task(task['task_id'])
        
        # Check that the task was not updated
        self.assertEqual(result['status'], 'completed')
        
        # Try to cancel a task that doesn't exist
        with self.assertRaises(ValueError):
            self.coordinator.cancel_task('unknown_task')
    
    def test_remove_task(self):
        """Test removing a task."""
        # Create an agent
        self.coordinator.create_agent('test', 'test_agent', ['test_capability'])
        
        # Create a task
        task = self.coordinator.create_task('test_task', 'Test task description', 'test_agent')
        
        # Remove the task
        result = self.coordinator.remove_task(task['task_id'])
        
        # Check the result
        self.assertTrue(result)
        
        # Check that the task was removed
        self.assertEqual(len(self.coordinator.tasks), 0)
        
        # Try to remove a task that doesn't exist
        result = self.coordinator.remove_task('unknown_task')
        
        # Check the result
        self.assertFalse(result)
    
    def test_get_status(self):
        """Test getting the coordinator status."""
        # Create an agent
        self.coordinator.create_agent('test', 'test_agent', ['test_capability'])
        
        # Create a group
        self.coordinator.create_agent_group('test_group', 'Test Group')
        
        # Create a task
        self.coordinator.create_task('test_task', 'Test task description', 'test_agent')
        
        # Set up the agent factory to return a list of agent types
        self.coordinator.agent_factory.get_available_agent_types.return_value = ['test', 'base']
        
        # Get the coordinator status
        status = self.coordinator.get_status()
        
        # Check the status
        self.assertEqual(status['coordinator_id'], 'test_coordinator')
        self.assertEqual(status['status'], 'initialized')
        self.assertEqual(status['agent_count'], 1)
        self.assertEqual(status['group_count'], 1)
        self.assertEqual(status['task_count'], 1)
        self.assertEqual(status['available_agent_types'], ['test', 'base'])
    
    def test_str_and_repr(self):
        """Test string and representation methods."""
        # Check string representation
        self.assertEqual(str(self.coordinator), 'Coordinator (ID: test_coordinator)')
        
        # Check detailed representation
        self.assertEqual(repr(self.coordinator), "Coordinator(coordinator_id='test_coordinator', agents=0, groups=0, tasks=0)")
        
        # Create an agent, group, and task
        self.coordinator.create_agent('test', 'test_agent', ['test_capability'])
        self.coordinator.create_agent_group('test_group', 'Test Group')
        self.coordinator.create_task('test_task', 'Test task description', 'test_agent')
        
        # Check detailed representation again
        self.assertEqual(repr(self.coordinator), "Coordinator(coordinator_id='test_coordinator', agents=1, groups=1, tasks=1)")

if __name__ == '__main__':
    unittest.main()
