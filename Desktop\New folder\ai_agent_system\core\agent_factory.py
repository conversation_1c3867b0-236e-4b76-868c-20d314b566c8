"""
Agent Factory Module

This module provides a factory for creating agents.
"""

import os
import importlib.util
import inspect
import logging
from typing import Dict, List, Any, Optional, Type

from ai_agent_system.core.base_agent import BaseAgent

# Configure logging
logger = logging.getLogger(__name__)

class AgentFactory:
    """
    Factory for creating agents.
    """
    
    def __init__(self):
        """Initialize the agent factory."""
        # Dictionary to store agent classes
        self.agent_classes = {}
        
        # Register built-in agent classes
        self._register_built_in_agents()
        
        logger.info("Agent Factory initialized")
    
    def _register_built_in_agents(self) -> None:
        """Register built-in agent classes."""
        # Register the base agent class
        self.register_agent_class('base', BaseAgent)
        
        # Register other built-in agent classes
        # This will be populated as we implement more agent classes
    
    def register_agent_class(self, agent_type: str, agent_class: Type[BaseAgent]) -> None:
        """
        Register an agent class.
        
        Args:
            agent_type (str): Type of the agent
            agent_class (type): Agent class
        """
        # Check if the agent class inherits from BaseAgent
        if not issubclass(agent_class, BaseAgent):
            raise ValueError(f"Agent class must inherit from BaseAgent: {agent_class.__name__}")
        
        # Register the agent class
        self.agent_classes[agent_type] = agent_class
        
        logger.info(f"Registered agent class: {agent_class.__name__} for type: {agent_type}")
    
    def register_agent_class_from_file(self, agent_type: str, file_path: str) -> None:
        """
        Register an agent class from a file.
        
        Args:
            agent_type (str): Type of the agent
            file_path (str): Path to the agent class file
        """
        try:
            # Check if the file exists
            if not os.path.exists(file_path):
                raise FileNotFoundError(f"Agent class file not found: {file_path}")
            
            # Load the module from the file
            module_name = os.path.basename(file_path).replace('.py', '')
            spec = importlib.util.spec_from_file_location(module_name, file_path)
            module = importlib.util.module_from_spec(spec)
            spec.loader.exec_module(module)
            
            # Find the agent class in the module
            agent_class = None
            for name, obj in inspect.getmembers(module):
                if inspect.isclass(obj) and issubclass(obj, BaseAgent) and obj != BaseAgent:
                    agent_class = obj
                    break
            
            if agent_class is None:
                raise ValueError(f"No agent class found in file: {file_path}")
            
            # Register the agent class
            self.register_agent_class(agent_type, agent_class)
            
        except Exception as e:
            logger.error(f"Error registering agent class from file: {e}")
            raise
    
    def create_agent(self, agent_type: str, agent_id: Optional[str] = None, capabilities: Optional[List[str]] = None, **kwargs) -> BaseAgent:
        """
        Create an agent.
        
        Args:
            agent_type (str): Type of the agent
            agent_id (str, optional): Unique identifier for the agent
            capabilities (list, optional): List of capabilities for the agent
            **kwargs: Additional arguments to pass to the agent constructor
            
        Returns:
            BaseAgent: The created agent
        """
        # Check if the agent type is registered
        if agent_type not in self.agent_classes:
            raise ValueError(f"Unknown agent type: {agent_type}")
        
        # Get the agent class
        agent_class = self.agent_classes[agent_type]
        
        # Create the agent
        agent = agent_class(agent_id=agent_id, agent_type=agent_type, capabilities=capabilities, **kwargs)
        
        logger.info(f"Created agent: {agent}")
        
        return agent
    
    def get_available_agent_types(self) -> List[str]:
        """
        Get a list of available agent types.
        
        Returns:
            list: List of available agent types
        """
        return list(self.agent_classes.keys())
    
    def get_agent_class(self, agent_type: str) -> Optional[Type[BaseAgent]]:
        """
        Get an agent class by type.
        
        Args:
            agent_type (str): Type of the agent
            
        Returns:
            type: The agent class or None if not found
        """
        return self.agent_classes.get(agent_type)
