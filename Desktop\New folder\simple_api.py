"""
Simple API for the Enhanced Agent Generator.

This module provides a Flask API for the SimpleEnhancedAgentGenerator.
"""

import os
import json
import logging
from flask import Flask, request, jsonify
from dotenv import load_dotenv

# Import the SimpleEnhancedAgentGenerator
from simple_enhanced_agent_generator import SimpleEnhancedAgentGenerator

# Import the SimpleLLMAgent
from simple_llm_agent import SimpleLLMAgent

# Import the SimpleDatasetAgent
from simple_dataset_agent import SimpleDatasetAgent

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Load environment variables from .env file
load_dotenv()

# Create Flask app
app = Flask(__name__)

# Create SimpleEnhancedAgentGenerator
generator = SimpleEnhancedAgentGenerator()

# Create SimpleLLMAgent
llm_agent = SimpleLLMAgent()

# Create SimpleDatasetAgent
dataset_agent = SimpleDatasetAgent()

@app.route('/health', methods=['GET'])
def health_check():
    """Health check endpoint."""
    return jsonify({
        'status': 'success',
        'message': 'API is running'
    })

@app.route('/api/agents', methods=['GET'])
def list_agents():
    """List all generated agents."""
    return jsonify({
        'status': 'success',
        'agents': generator.generated_agents
    })

@app.route('/api/agents', methods=['POST'])
def create_agent():
    """Create a new agent."""
    data = request.json
    
    agent_type = data.get('agent_type')
    capabilities = data.get('capabilities', [])
    requirements = data.get('requirements')
    
    if not agent_type:
        return jsonify({
            'status': 'error',
            'message': 'Agent type is required'
        }), 400
    
    if not capabilities:
        return jsonify({
            'status': 'error',
            'message': 'At least one capability is required'
        }), 400
    
    result = generator.generate_agent(agent_type, capabilities, requirements)
    
    if result['status'] == 'success':
        return jsonify(result), 201
    else:
        return jsonify(result), 400

@app.route('/api/agents/specialized', methods=['POST'])
def create_specialized_agent():
    """Create a specialized agent based on a task description."""
    data = request.json
    
    task_description = data.get('task_description')
    
    if not task_description:
        return jsonify({
            'status': 'error',
            'message': 'Task description is required'
        }), 400
    
    result = generator.create_specialized_agent(task_description)
    
    if result['status'] == 'success':
        return jsonify(result), 201
    else:
        return jsonify(result), 400

@app.route('/api/agents/<agent_id>', methods=['GET'])
def get_agent(agent_id):
    """Get information about a specific agent."""
    if agent_id not in generator.generated_agents:
        return jsonify({
            'status': 'error',
            'message': f'Agent not found: {agent_id}'
        }), 404
    
    return jsonify({
        'status': 'success',
        'agent': generator.generated_agents[agent_id]
    })

@app.route('/api/llm/text', methods=['POST'])
def generate_text():
    """Generate text using the LLM agent."""
    data = request.json
    
    prompt = data.get('prompt')
    options = data.get('options', {})
    
    if not prompt:
        return jsonify({
            'status': 'error',
            'message': 'Prompt is required'
        }), 400
    
    result = llm_agent.execute_task('text_generation', {
        'prompt': prompt,
        'options': options
    })
    
    if result['status'] == 'success':
        return jsonify(result)
    else:
        return jsonify(result), 400

@app.route('/api/llm/code', methods=['POST'])
def generate_code():
    """Generate code using the LLM agent."""
    data = request.json
    
    prompt = data.get('prompt')
    language = data.get('language')
    options = data.get('options', {})
    
    if not prompt:
        return jsonify({
            'status': 'error',
            'message': 'Prompt is required'
        }), 400
    
    result = llm_agent.execute_task('code_generation', {
        'prompt': prompt,
        'language': language,
        'options': options
    })
    
    if result['status'] == 'success':
        return jsonify(result)
    else:
        return jsonify(result), 400

@app.route('/api/datasets/apis', methods=['GET'])
def list_apis():
    """List all available APIs."""
    result = dataset_agent.execute_task('list_apis', {})
    
    if result['status'] == 'success':
        return jsonify(result)
    else:
        return jsonify(result), 400

@app.route('/api/datasets/apis/search', methods=['GET'])
def search_apis():
    """Search for APIs based on a query."""
    query = request.args.get('query', '')
    
    if not query:
        return jsonify({
            'status': 'error',
            'message': 'Query parameter is required'
        }), 400
    
    result = dataset_agent.execute_task('search_apis', {
        'query': query
    })
    
    if result['status'] == 'success':
        return jsonify(result)
    else:
        return jsonify(result), 400

@app.route('/api/datasets/data', methods=['GET'])
def get_data():
    """Get data from an API."""
    api_name = request.args.get('api_name')
    endpoint = request.args.get('endpoint')
    params = request.args.get('params', '{}')
    
    if not api_name:
        return jsonify({
            'status': 'error',
            'message': 'api_name parameter is required'
        }), 400
    
    if not endpoint:
        return jsonify({
            'status': 'error',
            'message': 'endpoint parameter is required'
        }), 400
    
    try:
        params = json.loads(params)
    except json.JSONDecodeError:
        return jsonify({
            'status': 'error',
            'message': 'params must be a valid JSON string'
        }), 400
    
    result = dataset_agent.execute_task('get_data', {
        'api_name': api_name,
        'endpoint': endpoint,
        'params': params
    })
    
    if result['status'] == 'success':
        return jsonify(result)
    else:
        return jsonify(result), 400

@app.route('/api/datasets/query', methods=['POST'])
def query_data():
    """Query data using a simplified query language."""
    data = request.json
    
    dataset = data.get('data')
    query = data.get('query')
    
    if not dataset:
        return jsonify({
            'status': 'error',
            'message': 'data is required'
        }), 400
    
    if not query:
        return jsonify({
            'status': 'error',
            'message': 'query is required'
        }), 400
    
    result = dataset_agent.execute_task('query_data', {
        'data': dataset,
        'query': query
    })
    
    if result['status'] == 'success':
        return jsonify(result)
    else:
        return jsonify(result), 400

if __name__ == '__main__':
    # Get port from environment variable or use default
    port = int(os.environ.get('PORT', 8002))

    # Run the app
    app.run(host='0.0.0.0', port=port, debug=True)
