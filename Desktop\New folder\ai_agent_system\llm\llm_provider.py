"""
LLM Provider Module

This module provides interfaces to various LLM providers.
"""

import os
import re
import logging
from abc import ABC, abstractmethod
from typing import Dict, List, Any, Optional, Union

# Import LangChain components
try:
    from langchain.llms import OpenAI as LangChainOpenAI
    from langchain.chat_models import ChatOpenAI
    from langchain.schema import HumanMessage, SystemMessage
    from langchain_openai import OpenAI, ChatOpenAI
    langchain_available = True
except ImportError:
    langchain_available = False

# Import settings
from ai_agent_system.config.settings import get_llm_provider_settings

# Configure logging
logger = logging.getLogger(__name__)

class LLMProvider(ABC):
    """Abstract base class for LLM providers."""

    @abstractmethod
    def generate_text(self, prompt: str, options: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        Generate text based on a prompt.

        Args:
            prompt (str): The prompt to send to the LLM
            options (dict, optional): Additional options for the LLM

        Returns:
            dict: The generated text and metadata
        """
        pass

    @abstractmethod
    def generate_code(self, prompt: str, language: Optional[str] = None, options: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        Generate code based on a prompt.

        Args:
            prompt (str): The prompt to send to the LLM
            language (str, optional): The programming language to generate
            options (dict, optional): Additional options for the LLM

        Returns:
            dict: The generated code and metadata
        """
        pass

    @abstractmethod
    def generate_agent_code(self, agent_type: str, capabilities: List[str], requirements: Optional[Dict[str, Any]] = None, options: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        Generate code for an agent.

        Args:
            agent_type (str): The type of agent to generate
            capabilities (list): List of capabilities the agent should have
            requirements (dict, optional): Specific requirements for the agent
            options (dict, optional): Additional options for the LLM

        Returns:
            dict: The generated agent code and metadata
        """
        pass

class OpenAIProvider(LLMProvider):
    """OpenAI LLM provider implementation."""

    def __init__(self, api_key: Optional[str] = None, model: str = "gpt-4"):
        """
        Initialize the OpenAI provider.

        Args:
            api_key (str, optional): OpenAI API key. If not provided, uses the settings.
            model (str, optional): The model to use. Default is "gpt-4".
        """
        # Get settings
        settings = get_llm_provider_settings('openai')

        self.api_key = api_key or settings['api_key']
        if not self.api_key:
            raise ValueError("OpenAI API key not provided and not found in settings")

        self.model = model or settings['model']
        self.api_url = "https://api.openai.com/v1/chat/completions"

        # Initialize LangChain client if available
        self.langchain_client = None
        if langchain_available:
            try:
                self.langchain_client = ChatOpenAI(
                    model_name=self.model,
                    openai_api_key=self.api_key,
                    temperature=0.7
                )
                logger.info(f"LangChain OpenAI client initialized with model: {self.model}")
            except Exception as e:
                logger.warning(f"Failed to initialize LangChain OpenAI client: {e}")

        logger.info(f"OpenAI provider initialized with model: {self.model}")

    def generate_text(self, prompt: str, options: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        Generate text using OpenAI.

        Args:
            prompt (str): The prompt to send to OpenAI
            options (dict, optional): Additional options for the API call

        Returns:
            dict: The generated text and metadata
        """
        options = options or {}

        try:
            # Use LangChain if available
            if self.langchain_client:
                # Set up messages
                messages = []

                # Add system message if provided
                system_prompt = options.get("system_prompt", "You are a helpful assistant.")
                messages.append(SystemMessage(content=system_prompt))

                # Add user message
                messages.append(HumanMessage(content=prompt))

                # Set temperature
                temperature = options.get("temperature", 0.7)
                self.langchain_client.temperature = temperature

                # Generate response
                response = self.langchain_client.generate([messages])

                # Extract generated text
                generated_text = response.generations[0][0].text

                # Extract token usage if available
                token_usage = {}
                if hasattr(response, 'llm_output') and response.llm_output:
                    token_usage = response.llm_output.get('token_usage', {})

                return {
                    "status": "success",
                    "text": generated_text,
                    "model": self.model,
                    "usage": token_usage
                }
            else:
                # Use direct API call
                try:
                    import requests
                except ImportError:
                    return {
                        "status": "error",
                        "message": "Requests library not available. Please install it with 'pip install requests'"
                    }

                headers = {
                    "Authorization": f"Bearer {self.api_key}",
                    "Content-Type": "application/json"
                }

                data = {
                    "model": options.get("model", self.model),
                    "messages": [
                        {"role": "system", "content": options.get("system_prompt", "You are a helpful assistant.")},
                        {"role": "user", "content": prompt}
                    ],
                    "temperature": options.get("temperature", 0.7),
                    "max_tokens": options.get("max_tokens", 1000),
                    "top_p": options.get("top_p", 1.0),
                    "frequency_penalty": options.get("frequency_penalty", 0.0),
                    "presence_penalty": options.get("presence_penalty", 0.0)
                }

                response = requests.post(self.api_url, headers=headers, json=data)
                response.raise_for_status()

                result = response.json()

                return {
                    "status": "success",
                    "text": result["choices"][0]["message"]["content"],
                    "model": result["model"],
                    "usage": result["usage"]
                }

        except Exception as e:
            logger.error(f"Error generating text with OpenAI: {e}")
            return {
                "status": "error",
                "message": str(e)
            }

    def generate_code(self, prompt: str, language: Optional[str] = None, options: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        Generate code using OpenAI.

        Args:
            prompt (str): The prompt to send to OpenAI
            language (str, optional): The programming language to generate
            options (dict, optional): Additional options for the API call

        Returns:
            dict: The generated code and metadata
        """
        options = options or {}

        # Create a code-specific system prompt
        language_str = f" in {language}" if language else ""
        system_prompt = options.get("system_prompt",
            f"You are an expert programmer. Generate clean, efficient, and well-documented code{language_str}. "
            f"Provide only the code without explanations unless specifically asked."
        )

        # Set code-specific options
        code_options = {
            "system_prompt": system_prompt,
            "temperature": options.get("temperature", 0.2),  # Lower temperature for more deterministic code
            "max_tokens": options.get("max_tokens", 2000),
            "top_p": options.get("top_p", 0.95),
            "frequency_penalty": options.get("frequency_penalty", 0.0),
            "presence_penalty": options.get("presence_penalty", 0.0),
            "model": options.get("model", self.model)
        }

        # Generate the code
        result = self.generate_text(prompt, code_options)

        if result["status"] == "success":
            # Extract code from the response
            code = result["text"]

            # Try to extract code blocks if they exist
            code_blocks = re.findall(r'```(?:\w+)?\n(.*?)```', code, re.DOTALL)
            if code_blocks:
                code = code_blocks[0]

            return {
                "status": "success",
                "code": code,
                "language": language,
                "model": result["model"],
                "usage": result["usage"]
            }

        return result

    def generate_agent_code(self, agent_type: str, capabilities: List[str], requirements: Optional[Dict[str, Any]] = None, options: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        Generate code for an agent using OpenAI.

        Args:
            agent_type (str): The type of agent to generate
            capabilities (list): List of capabilities the agent should have
            requirements (dict, optional): Specific requirements for the agent
            options (dict, optional): Additional options for the API call

        Returns:
            dict: The generated agent code and metadata
        """
        options = options or {}

        # Create a prompt for the agent code
        capabilities_str = ", ".join(capabilities)
        requirements_str = ""
        if requirements:
            requirements_str = "\nRequirements:\n" + "\n".join([f"- {k}: {v}" for k, v in requirements.items()])

        prompt = f"""
Generate Python code for a {agent_type} agent with the following capabilities: {capabilities_str}{requirements_str}

The agent should:
1. Inherit from BaseAgent
2. Implement methods for each capability
3. Include proper error handling and logging
4. Be well-documented with docstrings

The agent should follow this structure:
```python
from ai_agent_system.core.base_agent import BaseAgent

class {agent_type.capitalize()}Agent(BaseAgent):
    \"\"\"
    {agent_type.capitalize()} agent for the AI agent system.
    \"\"\"

    def __init__(self, agent_id=None, capabilities=None, **kwargs):
        \"\"\"
        Initialize the {agent_type} agent.

        Args:
            agent_id (str, optional): Unique identifier for the agent
            capabilities (list, optional): List of capabilities for the agent
            **kwargs: Additional arguments to pass to the parent constructor
        \"\"\"
        # Set default capabilities if not provided
        if capabilities is None:
            capabilities = [{', '.join([f"'{cap}'" for cap in capabilities])}]

        # Initialize the parent class
        super().__init__(agent_id=agent_id, agent_type='{agent_type}', capabilities=capabilities, **kwargs)

    # Implement capability methods here
```

Implement all the capability methods with realistic functionality.
"""

        # Generate the code
        result = self.generate_code(prompt, "python", options)

        if result["status"] == "success":
            return {
                "status": "success",
                "agent_code": result["code"],
                "agent_type": agent_type,
                "capabilities": capabilities,
                "model": result["model"],
                "usage": result["usage"]
            }

        return result

class AnthropicProvider(LLMProvider):
    """Anthropic Claude LLM provider implementation."""

    def __init__(self, api_key: Optional[str] = None, model: str = "claude-3-opus-20240229"):
        """
        Initialize the Anthropic provider.

        Args:
            api_key (str, optional): Anthropic API key. If not provided, uses the settings.
            model (str, optional): The model to use. Default is "claude-3-opus-20240229".
        """
        # Get settings
        settings = get_llm_provider_settings('anthropic')

        self.api_key = api_key or settings['api_key']
        if not self.api_key:
            raise ValueError("Anthropic API key not provided and not found in settings")

        self.model = model or settings['model']
        self.api_url = "https://api.anthropic.com/v1/messages"

        logger.info(f"Anthropic provider initialized with model: {self.model}")

    def generate_text(self, prompt: str, options: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        Generate text using Anthropic Claude.

        Args:
            prompt (str): The prompt to send to Claude
            options (dict, optional): Additional options for the API call

        Returns:
            dict: The generated text and metadata
        """
        options = options or {}

        try:
            try:
                import requests
            except ImportError:
                return {
                    "status": "error",
                    "message": "Requests library not available. Please install it with 'pip install requests'"
                }

            headers = {
                "x-api-key": self.api_key,
                "anthropic-version": "2023-06-01",
                "content-type": "application/json"
            }

            system_prompt = options.get("system_prompt", "")

            data = {
                "model": options.get("model", self.model),
                "messages": [
                    {"role": "user", "content": prompt}
                ],
                "max_tokens": options.get("max_tokens", 1000),
                "temperature": options.get("temperature", 0.7)
            }

            if system_prompt:
                data["system"] = system_prompt

            response = requests.post(self.api_url, headers=headers, json=data)
            response.raise_for_status()

            result = response.json()

            return {
                "status": "success",
                "text": result["content"][0]["text"],
                "model": result["model"],
                "usage": {
                    "input_tokens": result.get("usage", {}).get("input_tokens", 0),
                    "output_tokens": result.get("usage", {}).get("output_tokens", 0)
                }
            }

        except Exception as e:
            logger.error(f"Error generating text with Anthropic: {e}")
            return {
                "status": "error",
                "message": str(e)
            }

    def generate_code(self, prompt: str, language: Optional[str] = None, options: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        Generate code using Anthropic Claude.

        Args:
            prompt (str): The prompt to send to Claude
            language (str, optional): The programming language to generate
            options (dict, optional): Additional options for the API call

        Returns:
            dict: The generated code and metadata
        """
        options = options or {}

        # Create a code-specific system prompt
        language_str = f" in {language}" if language else ""
        system_prompt = options.get("system_prompt",
            f"You are an expert programmer. Generate clean, efficient, and well-documented code{language_str}. "
            f"Provide only the code without explanations unless specifically asked."
        )

        # Set code-specific options
        code_options = {
            "system_prompt": system_prompt,
            "temperature": options.get("temperature", 0.2),  # Lower temperature for more deterministic code
            "max_tokens": options.get("max_tokens", 2000),
            "model": options.get("model", self.model)
        }

        # Generate the code
        result = self.generate_text(prompt, code_options)

        if result["status"] == "success":
            # Extract code from the response
            code = result["text"]

            # Try to extract code blocks if they exist
            code_blocks = re.findall(r'```(?:\w+)?\n(.*?)```', code, re.DOTALL)
            if code_blocks:
                code = code_blocks[0]

            return {
                "status": "success",
                "code": code,
                "language": language,
                "model": result["model"],
                "usage": result["usage"]
            }

        return result

    def generate_agent_code(self, agent_type: str, capabilities: List[str], requirements: Optional[Dict[str, Any]] = None, options: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        Generate code for an agent using Anthropic Claude.

        Args:
            agent_type (str): The type of agent to generate
            capabilities (list): List of capabilities the agent should have
            requirements (dict, optional): Specific requirements for the agent
            options (dict, optional): Additional options for the API call

        Returns:
            dict: The generated agent code and metadata
        """
        # Use the same implementation as OpenAI
        options = options or {}

        # Create a prompt for the agent code
        capabilities_str = ", ".join(capabilities)
        requirements_str = ""
        if requirements:
            requirements_str = "\nRequirements:\n" + "\n".join([f"- {k}: {v}" for k, v in requirements.items()])

        prompt = f"""
Generate Python code for a {agent_type} agent with the following capabilities: {capabilities_str}{requirements_str}

The agent should:
1. Inherit from BaseAgent
2. Implement methods for each capability
3. Include proper error handling and logging
4. Be well-documented with docstrings

The agent should follow this structure:
```python
from ai_agent_system.core.base_agent import BaseAgent

class {agent_type.capitalize()}Agent(BaseAgent):
    \"\"\"
    {agent_type.capitalize()} agent for the AI agent system.
    \"\"\"

    def __init__(self, agent_id=None, capabilities=None, **kwargs):
        \"\"\"
        Initialize the {agent_type} agent.

        Args:
            agent_id (str, optional): Unique identifier for the agent
            capabilities (list, optional): List of capabilities for the agent
            **kwargs: Additional arguments to pass to the parent constructor
        \"\"\"
        # Set default capabilities if not provided
        if capabilities is None:
            capabilities = [{', '.join([f"'{cap}'" for cap in capabilities])}]

        # Initialize the parent class
        super().__init__(agent_id=agent_id, agent_type='{agent_type}', capabilities=capabilities, **kwargs)

    # Implement capability methods here
```

Implement all the capability methods with realistic functionality.
"""

        # Generate the code
        result = self.generate_code(prompt, "python", options)

        if result["status"] == "success":
            return {
                "status": "success",
                "agent_code": result["code"],
                "agent_type": agent_type,
                "capabilities": capabilities,
                "model": result["model"],
                "usage": result["usage"]
            }

        return result

class HuggingFaceProvider(LLMProvider):
    """Hugging Face LLM provider implementation."""

    def __init__(self, api_key: Optional[str] = None, model: str = "mistralai/Mistral-7B-Instruct-v0.1"):
        """
        Initialize the Hugging Face provider.

        Args:
            api_key (str, optional): Hugging Face API key. If not provided, uses the settings.
            model (str, optional): The model to use. Default is "mistralai/Mistral-7B-Instruct-v0.1".
        """
        # Get settings
        settings = get_llm_provider_settings('huggingface')

        self.api_key = api_key or settings['api_key']
        if not self.api_key:
            raise ValueError("Hugging Face API key not provided and not found in settings")

        self.model = model or settings['model']
        self.api_url = f"https://api-inference.huggingface.co/models/{self.model}"

        logger.info(f"Hugging Face provider initialized with model: {self.model}")

    def generate_text(self, prompt: str, options: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        Generate text using Hugging Face.

        Args:
            prompt (str): The prompt to send to Hugging Face
            options (dict, optional): Additional options for the API call

        Returns:
            dict: The generated text and metadata
        """
        options = options or {}

        try:
            import requests

            headers = {
                "Authorization": f"Bearer {self.api_key}",
                "Content-Type": "application/json"
            }

            # Format prompt based on model type
            if "mistral" in self.model.lower():
                formatted_prompt = f"<s>[INST] {prompt} [/INST]"
            elif "llama" in self.model.lower():
                formatted_prompt = f"<s>[INST] {prompt} [/INST]"
            else:
                formatted_prompt = prompt

            data = {
                "inputs": formatted_prompt,
                "parameters": {
                    "max_new_tokens": options.get("max_tokens", 1000),
                    "temperature": options.get("temperature", 0.7),
                    "top_p": options.get("top_p", 0.95),
                    "repetition_penalty": options.get("repetition_penalty", 1.1)
                }
            }

            response = requests.post(self.api_url, headers=headers, json=data)
            response.raise_for_status()

            result = response.json()

            # Handle different response formats
            if isinstance(result, list) and len(result) > 0 and "generated_text" in result[0]:
                text = result[0]["generated_text"]
            elif isinstance(result, dict) and "generated_text" in result:
                text = result["generated_text"]
            else:
                text = str(result)

            return {
                "status": "success",
                "text": text,
                "model": self.model
            }

        except Exception as e:
            logger.error(f"Error generating text with Hugging Face: {e}")
            return {
                "status": "error",
                "message": str(e)
            }

    def generate_code(self, prompt: str, language: Optional[str] = None, options: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        Generate code using Hugging Face.

        Args:
            prompt (str): The prompt to send to Hugging Face
            language (str, optional): The programming language to generate
            options (dict, optional): Additional options for the API call

        Returns:
            dict: The generated code and metadata
        """
        options = options or {}

        # Create a code-specific prompt
        language_str = f" in {language}" if language else ""
        code_prompt = f"Write code{language_str} for the following task: {prompt}\nOnly provide the code without explanations."

        # Set code-specific options
        code_options = {
            "temperature": options.get("temperature", 0.2),  # Lower temperature for more deterministic code
            "max_tokens": options.get("max_tokens", 2000),
            "top_p": options.get("top_p", 0.95)
        }

        # Generate the code
        result = self.generate_text(code_prompt, code_options)

        if result["status"] == "success":
            # Extract code from the response
            code = result["text"]

            # Try to extract code blocks if they exist
            code_blocks = re.findall(r'```(?:\w+)?\n(.*?)```', code, re.DOTALL)
            if code_blocks:
                code = code_blocks[0]

            return {
                "status": "success",
                "code": code,
                "language": language,
                "model": result["model"]
            }

        return result

    def generate_agent_code(self, agent_type: str, capabilities: List[str], requirements: Optional[Dict[str, Any]] = None, options: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        Generate code for an agent using Hugging Face.

        Args:
            agent_type (str): The type of agent to generate
            capabilities (list): List of capabilities the agent should have
            requirements (dict, optional): Specific requirements for the agent
            options (dict, optional): Additional options for the API call

        Returns:
            dict: The generated agent code and metadata
        """
        # Use the same implementation as OpenAI
        options = options or {}

        # Create a prompt for the agent code
        capabilities_str = ", ".join(capabilities)
        requirements_str = ""
        if requirements:
            requirements_str = "\nRequirements:\n" + "\n".join([f"- {k}: {v}" for k, v in requirements.items()])

        prompt = f"""
Generate Python code for a {agent_type} agent with the following capabilities: {capabilities_str}{requirements_str}

The agent should:
1. Inherit from BaseAgent
2. Implement methods for each capability
3. Include proper error handling and logging
4. Be well-documented with docstrings

The agent should follow this structure:
```python
from ai_agent_system.core.base_agent import BaseAgent

class {agent_type.capitalize()}Agent(BaseAgent):
    \"\"\"
    {agent_type.capitalize()} agent for the AI agent system.
    \"\"\"

    def __init__(self, agent_id=None, capabilities=None, **kwargs):
        \"\"\"
        Initialize the {agent_type} agent.

        Args:
            agent_id (str, optional): Unique identifier for the agent
            capabilities (list, optional): List of capabilities for the agent
            **kwargs: Additional arguments to pass to the parent constructor
        \"\"\"
        # Set default capabilities if not provided
        if capabilities is None:
            capabilities = [{', '.join([f"'{cap}'" for cap in capabilities])}]

        # Initialize the parent class
        super().__init__(agent_id=agent_id, agent_type='{agent_type}', capabilities=capabilities, **kwargs)

    # Implement capability methods here
```

Implement all the capability methods with realistic functionality.
"""

        # Generate the code
        result = self.generate_code(prompt, "python", options)

        if result["status"] == "success":
            return {
                "status": "success",
                "agent_code": result["code"],
                "agent_type": agent_type,
                "capabilities": capabilities,
                "model": result["model"]
            }

        return result
