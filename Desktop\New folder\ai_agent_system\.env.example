# Server settings
SERVER_HOST=0.0.0.0
SERVER_PORT=8000
DEBUG=True

# Database settings
DATABASE_URL=sqlite:///ai_agent_system.db
# For PostgreSQL: DATABASE_URL=postgres://postgres:postgres@localhost:5432/ai_agent_system

# Redis settings
REDIS_URL=redis://localhost:6379/0

# Security settings
SECRET_KEY=development-secret-key-change-in-production
JWT_EXPIRATION=3600

# LLM settings
OPENAI_API_KEY=your-openai-api-key-here
OPENAI_MODEL=gpt-4
ANTHROPIC_API_KEY=your-anthropic-api-key-here
ANTHROPIC_MODEL=claude-3-opus-20240229
HF_API_KEY=your-huggingface-api-key-here
HF_MODEL=mistralai/Mistral-7B-Instruct-v0.1

# Dataset settings
KAGGLE_USERNAME=your-kaggle-username-here
KAGGLE_KEY=your-kaggle-key-here
GOOGLE_APPLICATION_CREDENTIALS=path/to/your/google-credentials.json
AWS_ACCESS_KEY_ID=your-aws-access-key-id-here
AWS_SECRET_ACCESS_KEY=your-aws-secret-access-key-here
AWS_REGION=us-east-1

# Monitoring settings
PROMETHEUS_PORT=9090
GRAFANA_PORT=3000

# Logging settings
LOG_LEVEL=INFO
LOG_FILE=ai_agent_system.log

# Agent settings
AGENT_TEMPLATES_DIR=agent_templates
GENERATED_AGENTS_DIR=generated_agents
